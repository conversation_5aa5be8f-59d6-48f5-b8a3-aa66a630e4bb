import '../models/doctor.dart';
import '../models/doctor_schedule.dart';
import '../services/appointment_service.dart';
import '../widgets/doctor_filter_widget.dart';
import '../constants/appointment_constants.dart';

class DoctorFilterService {
  /// Filter doctors based on criteria
  static Future<List<Doctor>> filterDoctors({
    required List<Doctor> doctors,
    required DoctorFilter filter,
  }) async {
    print('🔍 Starting filter process - Total doctors: ${doctors.length}');
    print('🔍 Filter criteria: ${filter.description}');
    print('🔍 Filter details: date=${filter.date}, dayOfWeek=${filter.dayOfWeek}, timeSlot=${filter.timeSlot}');
    
    if (filter.isEmpty) {
      print('🔍 Filter is empty, returning all doctors');
      return doctors;
    }

    List<Doctor> filteredDoctors = [];

    for (final doctor in doctors) {
      print('\n🔍 Checking doctor: ${doctor.fullName}');
      final isMatch = await _isDoctorMatchingFilter(doctor, filter);
      if (isMatch) {
        print('✅ Doctor ${doctor.fullName} matches filter');
        filteredDoctors.add(doctor);
      } else {
        print('❌ Doctor ${doctor.fullName} does not match filter');
      }
    }

    print('\n🔍 Filter result: ${filteredDoctors.length}/${doctors.length} doctors match');
    return filteredDoctors;
  }

  /// Check if a doctor matches the filter criteria
  static Future<bool> _isDoctorMatchingFilter(Doctor doctor, DoctorFilter filter) async {
    // Check date/day filter
    if (filter.hasDateFilter) {
      final matchesDate = await _checkDateFilter(doctor, filter);
      if (!matchesDate) return false;
    }

    // Check time filter
    if (filter.hasTimeFilter) {
      final matchesTime = await _checkTimeFilter(doctor, filter);
      if (!matchesTime) return false;
    }

    return true;
  }

  /// Check if doctor is available on specified date or day
  static Future<bool> _checkDateFilter(Doctor doctor, DoctorFilter filter) async {
    String? targetDay;

    if (filter.date != null) {
      // Convert date to day of week in English for comparison with database
      targetDay = AppointmentConstants.getEnglishDayOfWeek(filter.date!);
    } else if (filter.dayOfWeek != null) {
      // Convert Vietnamese day to English for comparison with database
      targetDay = AppointmentConstants.dayToEnglish(filter.dayOfWeek!);
    }

    if (targetDay == null) return true;

    print('🔍 Checking date filter - Target day: $targetDay, Doctor available days: ${doctor.availableDays}');
    
    // Check if doctor works on this day
    return doctor.availableDays.contains(targetDay);
  }

  /// Check if doctor has the specified time slot available
  static Future<bool> _checkTimeFilter(Doctor doctor, DoctorFilter filter) async {
    if (filter.timeSlot == null) return true;

    try {
      // Get doctor's schedules
      final schedules = await AppointmentService.getDoctorSchedules(doctor.id!);

      // If filtering by specific date, check only that day's schedule
      if (filter.date != null) {
        final dayOfWeek = AppointmentConstants.getEnglishDayOfWeek(filter.date!);
        final daySchedules = schedules.where((s) => s.dayOfWeek == dayOfWeek).toList();
        
        print('🔍 Checking time filter for specific date - Day: $dayOfWeek, Schedules found: ${daySchedules.length}');
        
        return _hasTimeSlotInSchedules(daySchedules, filter.timeSlot!);
      }
      
      // If filtering by day of week, check that day's schedule
      if (filter.dayOfWeek != null) {
        final englishDay = AppointmentConstants.dayToEnglish(filter.dayOfWeek!);
        final daySchedules = schedules.where((s) => s.dayOfWeek == englishDay).toList();
        
        print('🔍 Checking time filter for day of week - Day: $englishDay, Schedules found: ${daySchedules.length}');
        
        return _hasTimeSlotInSchedules(daySchedules, filter.timeSlot!);
      }

      // If no specific day filter, check if any schedule has the time slot
      return _hasTimeSlotInSchedules(schedules, filter.timeSlot!);

    } catch (e) {
      print('❌ Error checking time filter for doctor ${doctor.fullName}: $e');
      return false;
    }
  }

  /// Check if any of the schedules contain the specified time slot
  static bool _hasTimeSlotInSchedules(List<DoctorSchedule> schedules, String timeSlot) {
    print('🔍 Checking time slot "$timeSlot" in ${schedules.length} schedules');
    
    for (final schedule in schedules) {
      final timeSlots = schedule.generateTimeSlots();
      print('   Schedule ${schedule.dayOfWeek}: ${timeSlots.length} slots (${timeSlots.take(5).join(', ')}...)');
      
      if (timeSlots.contains(timeSlot)) {
        print('   ✅ Found time slot "$timeSlot" in ${schedule.dayOfWeek} schedule');
        return true;
      }
    }
    
    print('   ❌ Time slot "$timeSlot" not found in any schedule');
    return false;
  }

  /// Get available time slots for filtered doctors
  static Future<List<String>> getAvailableTimeSlotsForDoctors({
    required List<Doctor> doctors,
    DateTime? date,
    String? dayOfWeek,
  }) async {
    Set<String> allTimeSlots = {};

    String? targetDay;
    if (date != null) {
      targetDay = AppointmentConstants.getDayOfWeek(date);
    } else if (dayOfWeek != null) {
      targetDay = dayOfWeek;
    }

    for (final doctor in doctors) {
      try {
        final schedules = await AppointmentService.getDoctorSchedules(doctor.id!);
        
        List<DoctorSchedule> relevantSchedules;
        if (targetDay != null) {
          relevantSchedules = schedules.where((s) => s.dayOfWeek == targetDay).toList();
        } else {
          relevantSchedules = schedules;
        }

        for (final schedule in relevantSchedules) {
          final timeSlots = schedule.generateTimeSlots();
          allTimeSlots.addAll(timeSlots);
        }
      } catch (e) {
        print('❌ Error getting time slots for doctor ${doctor.fullName}: $e');
      }
    }

    // Sort time slots
    final sortedSlots = allTimeSlots.toList();
    sortedSlots.sort((a, b) {
      final timeA = AppointmentConstants.timeToMinutes(a);
      final timeB = AppointmentConstants.timeToMinutes(b);
      return timeA.compareTo(timeB);
    });

    return sortedSlots;
  }

  /// Get summary statistics for filtered results
  static Map<String, dynamic> getFilterStats({
    required List<Doctor> originalDoctors,
    required List<Doctor> filteredDoctors,
    required DoctorFilter filter,
  }) {
    return {
      'totalDoctors': originalDoctors.length,
      'filteredDoctors': filteredDoctors.length,
      'filterDescription': filter.description,
      'hasFilter': !filter.isEmpty,
      'reductionPercentage': originalDoctors.isEmpty 
          ? 0.0 
          : ((originalDoctors.length - filteredDoctors.length) / originalDoctors.length * 100).round(),
    };
  }

  /// Get doctors grouped by specialization
  static Map<String, List<Doctor>> groupDoctorsBySpecialization(List<Doctor> doctors) {
    Map<String, List<Doctor>> grouped = {};
    
    for (final doctor in doctors) {
      final specialization = doctor.specialization;
      if (!grouped.containsKey(specialization)) {
        grouped[specialization] = [];
      }
      grouped[specialization]!.add(doctor);
    }
    
    return grouped;
  }

  /// Sort doctors by various criteria
  static List<Doctor> sortDoctors(List<Doctor> doctors, DoctorSortCriteria criteria) {
    final sortedDoctors = List<Doctor>.from(doctors);
    
    switch (criteria) {
      case DoctorSortCriteria.name:
        sortedDoctors.sort((a, b) => a.fullName.compareTo(b.fullName));
        break;
      case DoctorSortCriteria.specialization:
        sortedDoctors.sort((a, b) => a.specialization.compareTo(b.specialization));
        break;
      case DoctorSortCriteria.consultationFee:
        sortedDoctors.sort((a, b) => a.consultationFee.compareTo(b.consultationFee));
        break;
      case DoctorSortCriteria.experience:
        sortedDoctors.sort((a, b) {
          // Extract numeric value from experience string (e.g., "15 năm" -> 15)
          final expA = _extractExperienceYears(a.experience);
          final expB = _extractExperienceYears(b.experience);
          return expB.compareTo(expA); // Descending order (more experience first)
        });
        break;
    }
    
    return sortedDoctors;
  }

  static int _extractExperienceYears(String experience) {
    final regex = RegExp(r'(\d+)');
    final match = regex.firstMatch(experience);
    return match != null ? int.parse(match.group(1)!) : 0;
  }
}

enum DoctorSortCriteria {
  name,
  specialization,
  consultationFee,
  experience,
}
