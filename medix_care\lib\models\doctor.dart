class Doctor {
  final String? id;
  final String fullName;
  final String specialization;
  final String departmentId;
  final String title; // <PERSON><PERSON><PERSON> hà<PERSON>, học vị (<PERSON><PERSON><PERSON><PERSON> sĩ, <PERSON><PERSON><PERSON><PERSON> sĩ, <PERSON><PERSON><PERSON><PERSON> sư...)
  final String experience; // Số năm kinh nghiệm
  final String education; // Trình độ học vấn
  final String phone;
  final String email;
  final String description; // Mô tả về bác sĩ
  final String profileImage; // URL ảnh đại diện
  final double consultationFee; // Phí khám
  final List<String> availableDays; // Các ngày trong tuần có thể khám
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Doctor({
    this.id,
    required this.fullName,
    required this.specialization,
    required this.departmentId,
    required this.title,
    required this.experience,
    required this.education,
    required this.phone,
    required this.email,
    required this.description,
    this.profileImage = '',
    required this.consultationFee,
    required this.availableDays,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory Doctor.fromJson(Map<String, dynamic> json) {
    // Handle ID parsing more safely
    String? doctorId;
    if (json['_id'] != null) {
      if (json['_id'] is Map) {
        doctorId = json['_id']['\$oid'];
      } else {
        doctorId = json['_id'].toString();
      }
    }

    return Doctor(
      id: doctorId,
      fullName: json['fullName'] ?? '',
      specialization: json['specialization'] ?? '',
      departmentId: json['departmentId'] ?? '',
      title: json['title'] ?? '',
      experience: json['experience'] ?? '',
      education: json['education'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      description: json['description'] ?? '',
      profileImage: json['profileImage'] ?? '',
      consultationFee: (json['consultationFee'] ?? 0).toDouble(),
      availableDays: List<String>.from(json['availableDays'] ?? []),
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'fullName': fullName,
      'specialization': specialization,
      'departmentId': departmentId,
      'title': title,
      'experience': experience,
      'education': education,
      'phone': phone,
      'email': email,
      'description': description,
      'profileImage': profileImage,
      'consultationFee': consultationFee,
      'availableDays': availableDays,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  Doctor copyWith({
    String? id,
    String? fullName,
    String? specialization,
    String? departmentId,
    String? title,
    String? experience,
    String? education,
    String? phone,
    String? email,
    String? description,
    String? profileImage,
    double? consultationFee,
    List<String>? availableDays,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Doctor(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      specialization: specialization ?? this.specialization,
      departmentId: departmentId ?? this.departmentId,
      title: title ?? this.title,
      experience: experience ?? this.experience,
      education: education ?? this.education,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      description: description ?? this.description,
      profileImage: profileImage ?? this.profileImage,
      consultationFee: consultationFee ?? this.consultationFee,
      availableDays: availableDays ?? this.availableDays,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() => '$title $fullName - $specialization';
}
