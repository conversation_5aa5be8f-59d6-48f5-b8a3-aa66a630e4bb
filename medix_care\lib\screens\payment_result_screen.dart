import 'package:flutter/material.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../services/appointment_service.dart';
import '../constants/app_constants.dart';
import 'my_appointments_screen.dart';

class PaymentResultScreen extends StatefulWidget {
  final Appointment appointment;
  final Doctor doctor;
  final bool status;
  final String? responseCode;

  const PaymentResultScreen({
    super.key,
    required this.appointment,
    required this.doctor,
    required this.status,
    this.responseCode,
  });

  @override
  State<PaymentResultScreen> createState() => _PaymentResultScreenState();
}

class _PaymentResultScreenState extends State<PaymentResultScreen> {
  bool _isLoading = true;
  bool _isUpdated = false;
  String _error = '';

  @override
  void initState() {
    super.initState();
    _updateAppointmentPaymentStatus();
  }

  Future<void> _updateAppointmentPaymentStatus() async {
    try {
      if (widget.status) {
        // Cập nhật trạng thái thanh toán trong cơ sở dữ liệu
        await AppointmentService.updateAppointmentPaymentStatus(
          widget.appointment.id!,
          isPaid: true,
          paymentMethod: 'VNPay',
        );
        
        setState(() {
          _isLoading = false;
          _isUpdated = true;
        });
      } else {
        setState(() {
          _isLoading = false;
          _isUpdated = false;
          _error = 'Thanh toán không thành công';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isUpdated = false;
        _error = 'Lỗi cập nhật trạng thái thanh toán: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kết quả thanh toán'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
        automaticallyImplyLeading: false,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildBody(),
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildResultCard(),
          const SizedBox(height: 24),
          _buildButtons(),
        ],
      ),
    );
  }

  Widget _buildResultCard() {
    final bool isSuccess = widget.status && _isUpdated;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              isSuccess ? Icons.check_circle : Icons.cancel,
              size: 70,
              color: isSuccess ? Colors.green : Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              isSuccess ? 'Thanh toán thành công' : 'Thanh toán thất bại',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isSuccess 
                  ? 'Cảm ơn bạn đã thanh toán phí khám bệnh'
                  : _error.isEmpty ? 'Có lỗi xảy ra trong quá trình thanh toán' : _error,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: isSuccess ? Colors.black54 : Colors.red[700],
              ),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            _buildInfoRow('Mã lịch hẹn', widget.appointment.id ?? 'N/A'),
            _buildInfoRow('Bác sĩ', widget.doctor.fullName),
            _buildInfoRow('Ngày khám', _formatDate(widget.appointment.appointmentDate)),
            _buildInfoRow('Giờ khám', widget.appointment.appointmentTime),
            _buildInfoRow(
              'Phí khám', 
              '${widget.appointment.consultationFee.toStringAsFixed(0)} VNĐ'
            ),
            _buildInfoRow(
              'Trạng thái thanh toán',
              isSuccess ? 'Đã thanh toán' : 'Chưa thanh toán',
              valueColor: isSuccess ? Colors.green : Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.black54,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const MyAppointmentsScreen(),
              ),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppConstants.primaryBlue,
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: const Text(
            'Xem lịch hẹn của tôi',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        const SizedBox(height: 12),
        OutlinedButton(
          onPressed: () {
            Navigator.of(context).popUntil((route) => route.isFirst);
          },
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            side: BorderSide(color: AppConstants.primaryBlue),
          ),
          child: Text(
            'Về trang chủ',
            style: TextStyle(
              fontSize: 16,
              color: AppConstants.primaryBlue,
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
