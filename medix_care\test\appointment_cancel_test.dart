import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/models/appointment.dart';

void main() {
  group('Appointment Cancel Tests', () {
    test('should validate appointment cancellation rules', () {
      final now = DateTime.now();
      
      // Test case 1: Appointment far in the future (can cancel)
      final futureAppointment = Appointment(
        id: 'test_id_1',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: now.add(const Duration(days: 1)),
        appointmentTime: '10:00',
        symptoms: 'Headache',
        notes: 'Regular checkup',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.pending,
      );

      expect(futureAppointment.canCancel, isTrue);

      // Test case 2: Appointment too soon (cannot cancel)
      final soonAppointment = Appointment(
        id: 'test_id_2',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: now,
        appointmentTime: '${now.add(const Duration(hours: 1)).hour.toString().padLeft(2, '0')}:00',
        symptoms: 'Fever',
        notes: 'Urgent',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.pending,
      );

      expect(soonAppointment.canCancel, isFalse);

      // Test case 3: Already completed appointment (cannot cancel)
      final completedAppointment = Appointment(
        id: 'test_id_3',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: now.add(const Duration(days: 1)),
        appointmentTime: '14:00',
        symptoms: 'Checkup',
        notes: 'Follow up',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.completed,
      );

      expect(completedAppointment.canCancel, isFalse);

      // Test case 4: Already cancelled appointment (cannot cancel again)
      final cancelledAppointment = Appointment(
        id: 'test_id_4',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: now.add(const Duration(days: 1)),
        appointmentTime: '16:00',
        symptoms: 'Consultation',
        notes: 'General',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Patient cancelled',
      );

      expect(cancelledAppointment.canCancel, isFalse);
    });

    test('should handle cancellation reasons correctly', () {
      final appointment = Appointment(
        id: 'test_id',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '10:00',
        symptoms: 'Regular checkup',
        notes: 'Annual checkup',
        consultationFee: 300000.0,
        createdAt: DateTime.now(),
        status: AppointmentStatus.pending,
      );

      // Cancel with custom reason
      final cancelledWithReason = appointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Bệnh nhân có việc đột xuất',
        updatedAt: DateTime.now(),
      );

      expect(cancelledWithReason.status, equals(AppointmentStatus.cancelled));
      expect(cancelledWithReason.cancelledReason, equals('Bệnh nhân có việc đột xuất'));
      expect(cancelledWithReason.statusVN, equals('Đã hủy'));

      // Cancel with default reason
      final cancelledDefault = appointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Bệnh nhân hủy lịch',
        updatedAt: DateTime.now(),
      );

      expect(cancelledDefault.cancelledReason, equals('Bệnh nhân hủy lịch'));
    });

    test('should preserve other appointment data when cancelling', () {
      final originalAppointment = Appointment(
        id: 'test_id',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '10:00',
        symptoms: 'Headache and fever',
        notes: 'Patient reports symptoms for 3 days',
        doctorNotes: 'Previous consultation notes',
        consultationFee: 500000.0,
        isPaid: true,
        paymentMethod: 'Credit Card',
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        status: AppointmentStatus.confirmed,
        confirmedAt: DateTime.now().subtract(const Duration(days: 1)),
      );

      final cancelledAppointment = originalAppointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Emergency situation',
        updatedAt: DateTime.now(),
      );

      // Check that all original data is preserved
      expect(cancelledAppointment.id, equals(originalAppointment.id));
      expect(cancelledAppointment.patientId, equals(originalAppointment.patientId));
      expect(cancelledAppointment.doctorId, equals(originalAppointment.doctorId));
      expect(cancelledAppointment.departmentId, equals(originalAppointment.departmentId));
      expect(cancelledAppointment.appointmentDate, equals(originalAppointment.appointmentDate));
      expect(cancelledAppointment.appointmentTime, equals(originalAppointment.appointmentTime));
      expect(cancelledAppointment.symptoms, equals(originalAppointment.symptoms));
      expect(cancelledAppointment.notes, equals(originalAppointment.notes));
      expect(cancelledAppointment.doctorNotes, equals(originalAppointment.doctorNotes));
      expect(cancelledAppointment.consultationFee, equals(originalAppointment.consultationFee));
      expect(cancelledAppointment.isPaid, equals(originalAppointment.isPaid));
      expect(cancelledAppointment.paymentMethod, equals(originalAppointment.paymentMethod));
      expect(cancelledAppointment.createdAt, equals(originalAppointment.createdAt));
      expect(cancelledAppointment.confirmedAt, equals(originalAppointment.confirmedAt));

      // Check that cancellation-specific data is set
      expect(cancelledAppointment.status, equals(AppointmentStatus.cancelled));
      expect(cancelledAppointment.cancelledReason, equals('Emergency situation'));
      expect(cancelledAppointment.updatedAt, isNotNull);
    });

    test('should handle JSON serialization with cancelled reason', () {
      final appointment = Appointment(
        id: 'test_id',
        patientId: 'patient_1',
        doctorId: 'doctor_1',
        departmentId: 'dept_1',
        appointmentDate: DateTime.now(),
        appointmentTime: '10:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: DateTime.now(),
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Patient cancelled due to emergency',
      );

      // Convert to JSON
      final json = appointment.toJson();
      expect(json['status'], equals('cancelled'));
      expect(json['cancelledReason'], equals('Patient cancelled due to emergency'));

      // Convert back from JSON
      final fromJson = Appointment.fromJson(json);
      expect(fromJson.status, equals(AppointmentStatus.cancelled));
      expect(fromJson.cancelledReason, equals('Patient cancelled due to emergency'));
      expect(fromJson.statusVN, equals('Đã hủy'));
    });

    test('should handle missing cancelled reason in JSON', () {
      final jsonWithoutReason = {
        '_id': 'test_id',
        'patientId': 'patient_1',
        'doctorId': 'doctor_1',
        'departmentId': 'dept_1',
        'appointmentDate': DateTime.now().toIso8601String(),
        'appointmentTime': '10:00',
        'status': 'cancelled',
        'symptoms': 'Test symptoms',
        'notes': 'Test notes',
        'consultationFee': 300000.0,
        'isPaid': false,
        'createdAt': DateTime.now().toIso8601String(),
      };

      final appointment = Appointment.fromJson(jsonWithoutReason);
      expect(appointment.status, equals(AppointmentStatus.cancelled));
      expect(appointment.cancelledReason, isNull);
    });
  });
}
