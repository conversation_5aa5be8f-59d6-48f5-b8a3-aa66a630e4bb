import 'package:flutter_test/flutter_test.dart';

import '../../lib/models/appointment.dart';

void main() {
  group('Payment Button Logic Tests', () {
    test('should determine if payment button should be shown', () {
      // Test case 1: Unpaid pending appointment - should show payment button
      final unpaidPendingAppointment = Appointment(
        id: 'appointment123',
        patientId: 'user123',
        doctorId: 'doctor123',
        departmentId: 'dept123',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '09:00',
        status: AppointmentStatus.pending,
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000,
        isPaid: false, // Chưa thanh toán
        createdAt: DateTime.now(),
      );

      // Test case 2: Paid pending appointment - should NOT show payment button
      final paidPendingAppointment = Appointment(
        id: 'appointment124',
        patientId: 'user123',
        doctorId: 'doctor123',
        departmentId: 'dept123',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '10:00',
        status: AppointmentStatus.pending,
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000,
        isPaid: true, // Đã thanh toán
        createdAt: DateTime.now(),
      );

      // Test case 3: Completed appointment - should NOT show payment button
      final completedAppointment = Appointment(
        id: 'appointment125',
        patientId: 'user123',
        doctorId: 'doctor123',
        departmentId: 'dept123',
        appointmentDate: DateTime.now().subtract(const Duration(days: 1)),
        appointmentTime: '09:00',
        status: AppointmentStatus.completed,
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000,
        isPaid: false, // Chưa thanh toán nhưng đã khám xong
        createdAt: DateTime.now(),
      );

      // Assertions
      expect(unpaidPendingAppointment.isPaid, false);
      expect(unpaidPendingAppointment.status, AppointmentStatus.pending);

      expect(paidPendingAppointment.isPaid, true);
      expect(paidPendingAppointment.status, AppointmentStatus.pending);

      expect(completedAppointment.isPaid, false);
      expect(completedAppointment.status, AppointmentStatus.completed);
    });
  });
}
