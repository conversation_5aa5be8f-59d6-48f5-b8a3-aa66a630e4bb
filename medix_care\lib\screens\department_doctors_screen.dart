import 'package:flutter/material.dart';
import '../models/department.dart';
import '../models/doctor.dart';
import '../services/appointment_service.dart';
import '../services/doctor_filter_service.dart';
import '../widgets/filter_popup_dialog.dart';
import '../widgets/doctor_filter_widget.dart';
import 'book_appointment_screen.dart';

class DepartmentDoctorsScreen extends StatefulWidget {
  final Department department;

  const DepartmentDoctorsScreen({
    super.key,
    required this.department,
  });

  @override
  State<DepartmentDoctorsScreen> createState() => _DepartmentDoctorsScreenState();
}

class _DepartmentDoctorsScreenState extends State<DepartmentDoctorsScreen> {
  List<Doctor> doctors = [];
  List<Doctor> filteredDoctors = [];
  bool isLoading = true;
  String? error;
  DoctorFilter currentFilter = const DoctorFilter();

  @override
  void initState() {
    super.initState();
    _loadDoctors();
  }

  Future<void> _loadDoctors() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      await AppointmentService.initialize();
      final loadedDoctors = await AppointmentService.getDoctorsByDepartment(widget.department.id!);
      
      setState(() {
        doctors = loadedDoctors;
        filteredDoctors = loadedDoctors;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  Future<void> _showFilterDialog() async {
    final result = await FilterDialog.show(
      context: context,
      initialFilter: currentFilter,
    );

    if (result != null) {
      await _applyFilter(result);
    }
  }

  Future<void> _applyFilter(DoctorFilter filter) async {
    setState(() {
      currentFilter = filter;
      isLoading = true;
    });

    try {
      if (filter.isEmpty) {
        setState(() {
          filteredDoctors = doctors;
          isLoading = false;
        });
      } else {
        final filtered = await DoctorFilterService.filterDoctors(
          doctors: doctors,
          filter: filter,
        );
        
        setState(() {
          filteredDoctors = filtered;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  void _clearFilter() {
    _applyFilter(const DoctorFilter());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.department.name),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          // Filter Button
          Stack(
            children: [
              IconButton(
                onPressed: _showFilterDialog,
                icon: const Icon(Icons.filter_list),
                tooltip: 'Bộ lọc tìm kiếm',
              ),
              if (!currentFilter.isEmpty)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    width: 8,
                    height: 8,
                    decoration: const BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDoctors,
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter Summary (if filter is applied)
          if (!currentFilter.isEmpty)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Bộ lọc: ${currentFilter.description}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  TextButton.icon(
                    onPressed: _clearFilter,
                    icon: const Icon(Icons.clear, size: 16),
                    label: const Text('Xóa'),
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
                ],
              ),
            ),
          
          // Doctors List
          Expanded(
            child: _buildBody(),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải danh sách bác sĩ...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadDoctors,
              icon: const Icon(Icons.refresh),
              label: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (filteredDoctors.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              currentFilter.isEmpty ? Icons.medical_services : Icons.search_off,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              currentFilter.isEmpty 
                ? 'Không có bác sĩ nào trong khoa này'
                : 'Không tìm thấy bác sĩ phù hợp',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              currentFilter.isEmpty
                ? 'Vui lòng thử lại sau'
                : 'Thử điều chỉnh bộ lọc tìm kiếm',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (!currentFilter.isEmpty) ...[
              const SizedBox(height: 16),
              OutlinedButton.icon(
                onPressed: _clearFilter,
                icon: const Icon(Icons.clear),
                label: const Text('Xóa bộ lọc'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredDoctors.length,
      itemBuilder: (context, index) {
        final doctor = filteredDoctors[index];
        return _buildDoctorCard(doctor);
      },
    );
  }

  Widget _buildDoctorCard(Doctor doctor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () => _bookAppointment(doctor),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    child: Icon(
                      Icons.person,
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          doctor.fullName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          doctor.specialization,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_ios, size: 16),
                ],
              ),
              if (doctor.experience.isNotEmpty) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.work_outline, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      '${doctor.experience} năm kinh nghiệm',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _bookAppointment(Doctor doctor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BookAppointmentScreen(
          doctor: doctor,
          department: widget.department,
          initialDate: currentFilter.date,
          initialTimeSlot: currentFilter.timeSlot,
        ),
      ),
    ).then((_) {
      // Refresh the screen when returning from booking
      _loadDoctors();
    });
  }
}
