import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/services/appointment_service.dart';
import 'package:medix_care/services/jwt_service.dart';

void main() {
  test('Debug appointments issue', () async {
    print('🔍 Debug Appointments Issue');
    
    try {
      // Initialize service
      final connected = await AppointmentService.initialize();
      expect(connected, isTrue);
      print('✅ Connected to MongoDB');
      
      // Check current user
      final user = await JWTService.getCurrentUser();
<<<<<<< HEAD
      final userIdShort = user?.id != null ? (user!.id!.length > 8 ? user.id!.substring(0, 8) : user.id) : 'N/A';
      print('👤 Current user: ${user?.fullName} (ID: $userIdShort)');
=======
      print('👤 Current user: ${user?.fullName} (ID: ${user?.id})');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      
      // Get all appointments to see what's in DB
      print('\n📅 Getting ALL appointments from database...');
      final allAppointments = await AppointmentService.getAllAppointments();
      print('Found ${allAppointments.length} total appointments');
      
      for (final appointment in allAppointments) {
<<<<<<< HEAD
        final appointmentIdShort = appointment.id?.substring(0, 8) ?? 'N/A';
        final patientIdShort = appointment.patientId.length > 8 ? appointment.patientId.substring(0, 8) : appointment.patientId;
        final doctorIdShort = appointment.doctorId.length > 8 ? appointment.doctorId.substring(0, 8) : appointment.doctorId;
        
        print('  - Appointment ID: $appointmentIdShort');
        print('    Patient ID: $patientIdShort');
        print('    Doctor ID: $doctorIdShort');
=======
        print('  - Appointment ID: ${appointment.id}');
        print('    Patient ID: ${appointment.patientId}');
        print('    Doctor ID: ${appointment.doctorId}');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
        print('    Date: ${appointment.appointmentDate}');
        print('    Time: ${appointment.appointmentTime}');
        print('    Status: ${appointment.status}');
        print('    Symptoms: ${appointment.symptoms}');
        print('');
      }
      
      if (user != null) {
        // Get appointments for this user
        print('\n📅 Getting appointments for user: ${user.id}');
        final userAppointments = await AppointmentService.getAppointmentsByPatient(user.id!);
        print('Found ${userAppointments.length} appointments for this user');
        
        for (final appointment in userAppointments) {
          print('  - User appointment: ${appointment.id}');
          print('    Date: ${appointment.appointmentDate}');
          print('    Time: ${appointment.appointmentTime}');
          print('');
        }
      }
      
      await AppointmentService.close();
      
    } catch (e) {
      print('❌ Error: $e');
      fail('Debug test failed: $e');
    }
  });
}
