import 'package:flutter/material.dart';
import 'dart:async';
import '../constants/app_constants.dart';
import '../services/email_service.dart';
import '../services/mongodb_direct_service.dart';
import '../utils/validation_helper.dart';
import 'reset_password_screen.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _otpController = TextEditingController();
  
  bool _isLoading = false;
  bool _isResending = false;
  bool _showOtpInput = false;
  String? _storedResetCode;
  String? _userEmail;
  String? _userId;
  int _countdown = 60;
  Timer? _timer;

  @override
  void dispose() {
    _emailController.dispose();
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _sendResetEmail() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final email = _emailController.text.trim();
      
      // Initialize MongoDB connection if not already done
      print('🔄 Ensuring MongoDB connection...');
      await MongoDBDirectService.initialize();
      print('✅ MongoDB connection ensured');
      
      // Check if user exists
      final user = await MongoDBDirectService.getUserByEmail(email);
      if (user == null) {
        // Don't reveal if email exists or not for security
        _showEmailSentMessage();
        return;
      }

      // Generate reset OTP code
      _storedResetCode = EmailService.generateVerificationCode();
      _userEmail = email;
      _userId = user.id!;
      
      // Store reset code in database with expiry
      await MongoDBDirectService.updateUser(user.id!, {
        'resetPasswordToken': _storedResetCode,
        'resetPasswordExpiry': DateTime.now().add(const Duration(minutes: 15)).toIso8601String(),
      });

      // Send reset email with OTP
      final success = await EmailService.sendPasswordResetEmail(
        toEmail: email,
        userName: user.fullName,
        resetCode: _storedResetCode!,
      );

      if (success) {
        setState(() {
          _showOtpInput = true;
        });
        _startCountdown();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Mã OTP đã được gửi đến $email'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('Không thể gửi email đặt lại mật khẩu');
      }
    } catch (e) {
      print('❌ Error in _sendResetEmail: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showEmailSentMessage() {
    setState(() {
      _showOtpInput = true;
    });
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Nếu email tồn tại, mã OTP đã được gửi'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _startCountdown() {
    setState(() {
      _countdown = 60;
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _resendOTP() async {
    if (_userEmail == null) return;
    
    setState(() {
      _isResending = true;
    });
    
    try {
      // Generate new reset code
      _storedResetCode = EmailService.generateVerificationCode();
      
      // Update reset code in database
      await MongoDBDirectService.updateUser(_userId!, {
        'resetPasswordToken': _storedResetCode,
        'resetPasswordExpiry': DateTime.now().add(const Duration(minutes: 15)).toIso8601String(),
      });

      // Get user info for sending email
      final user = await MongoDBDirectService.getUserByEmail(_userEmail!);
      if (user != null) {
        final success = await EmailService.sendPasswordResetEmail(
          toEmail: _userEmail!,
          userName: user.fullName,
          resetCode: _storedResetCode!,
        );

        if (success) {
          _startCountdown();
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Mã OTP mới đã được gửi'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      print('❌ Error resending OTP: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  Future<void> _verifyOTP() async {
    if (_otpController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng nhập mã OTP'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final enteredOTP = _otpController.text.trim();
      
      if (enteredOTP != _storedResetCode) {
        throw Exception('Mã OTP không đúng');
      }

      // Navigate to reset password screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => ResetPasswordScreen(
              email: _userEmail!,
              userId: _userId!,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quên mật khẩu'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Header icon
                Icon(
                  Icons.lock_reset,
                  size: 80,
                  color: AppConstants.primaryBlue,
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                Text(
                  'Quên mật khẩu?',
                  style: const TextStyle(
                    fontSize: AppConstants.titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryBlue,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                Text(
                  _showOtpInput 
                    ? 'Nhập mã OTP đã được gửi đến email của bạn'
                    : 'Nhập email của bạn để nhận mã OTP đặt lại mật khẩu',
                  style: const TextStyle(
                    fontSize: AppConstants.subtitleFontSize,
                    color: AppConstants.greyText,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                if (_showOtpInput && _userEmail != null) ...[
                  const SizedBox(height: AppConstants.paddingSmall),
                  Text(
                    _userEmail!,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppConstants.primaryBlue,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Email input or OTP input
                if (!_showOtpInput) ...[
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    enabled: !_isLoading,
                    decoration: const InputDecoration(
                      labelText: 'Email',
                      hintText: 'Nhập email của bạn',
                      prefixIcon: Icon(Icons.email),
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) => ValidationHelper.getEmailValidationError(value ?? ''),
                  ),
                ] else ...[
                  TextFormField(
                    controller: _otpController,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    maxLength: 6,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 8,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'Mã OTP',
                      hintText: '000000',
                      prefixIcon: Icon(Icons.security),
                      border: OutlineInputBorder(),
                      counterText: '',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Vui lòng nhập mã OTP';
                      }
                      if (value.length != 6) {
                        return 'Mã OTP phải có 6 chữ số';
                      }
                      return null;
                    },
                  ),
                ],
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Send reset button or Verify OTP button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : (_showOtpInput ? _verifyOTP : _sendResetEmail),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryBlue,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: AppConstants.whiteText)
                        : Text(
                            _showOtpInput ? 'Xác thực OTP' : 'Gửi mã OTP',
                            style: const TextStyle(
                              color: AppConstants.whiteText,
                              fontSize: AppConstants.buttonFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Resend OTP or Back to login
                if (_showOtpInput) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Không nhận được mã? ',
                        style: TextStyle(color: AppConstants.greyText),
                      ),
                      TextButton(
                        onPressed: (_countdown > 0 || _isResending) ? null : _resendOTP,
                        child: _isResending
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : Text(
                                _countdown > 0 
                                    ? 'Gửi lại (${_countdown}s)'
                                    : 'Gửi lại mã',
                                style: TextStyle(
                                  color: _countdown > 0 
                                      ? AppConstants.greyText 
                                      : AppConstants.primaryBlue,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                      ),
                    ],
                  ),
                ] else ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Nhớ mật khẩu? ',
                        style: TextStyle(color: AppConstants.greyText),
                      ),
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text(
                          'Đăng nhập',
                          style: TextStyle(
                            color: AppConstants.primaryBlue,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Instructions
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _showOtpInput ? '� Hướng dẫn OTP:' : '�🔒 Lưu ý bảo mật:',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                      ),
                      const SizedBox(height: 8),
                      if (_showOtpInput) ...[
                        const Text('• Mã OTP có hiệu lực trong 15 phút'),
                        const Text('• Kiểm tra hộp thư đến và thư mục spam'),
                        const Text('• Nhập đúng 6 chữ số đã nhận'),
                        const Text('• Nếu không nhận được, bấm "Gửi lại mã"'),
                      ] else ...[
                        const Text('• Nhập đúng email đã đăng ký'),
                        const Text('• Kiểm tra hộp thư đến và thư mục spam'),
                        const Text('• Mã OTP có hiệu lực trong 15 phút'),
                        const Text('• Liên hệ hỗ trợ nếu không nhận được'),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
