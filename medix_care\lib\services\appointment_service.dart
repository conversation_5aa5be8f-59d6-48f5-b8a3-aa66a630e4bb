import 'package:mongo_dart/mongo_dart.dart';
import '../models/department.dart';
import '../models/doctor.dart';
import '../models/doctor_schedule.dart';
import '../models/appointment.dart';
import '../config/mongodb_direct_config.dart';

class AppointmentService {
  static Db? _db;
  static DbCollection? _departmentsCollection;
  static DbCollection? _doctorsCollection;
  static DbCollection? _schedulesCollection;
  static DbCollection? _appointmentsCollection;

  /// Initialize MongoDB connection
  static Future<bool> initialize() async {
    try {
      _db = await Db.create(MongoDBConfig.connectionString);
      await _db!.open();
      
      _departmentsCollection = _db!.collection('departments');
      _doctorsCollection = _db!.collection('doctors');
      _schedulesCollection = _db!.collection('doctor_schedules');
      _appointmentsCollection = _db!.collection('appointments');
      
      print('✅ Appointment Service connected to MongoDB successfully!');
      print('Database: ${MongoDBConfig.database}');
      print('Collections: departments, doctors, doctor_schedules, appointments');
      
      return true;
    } catch (e) {
      print('❌ Appointment Service MongoDB connection failed: $e');
      return false;
    }
  }

  /// Close MongoDB connection
  static Future<void> close() async {
    await _db?.close();
    _db = null;
    _departmentsCollection = null;
    _doctorsCollection = null;
    _schedulesCollection = null;
    _appointmentsCollection = null;
  }

  /// Clean ObjectId format from string 
  static String _cleanObjectId(String objectIdString) {
    if (objectIdString.isEmpty) {
      return objectIdString;
    }
    
    // Handle format: ObjectId("hexadecimal")
    if (objectIdString.startsWith('ObjectId("') && objectIdString.endsWith('")')) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    // Handle format: ObjectId('hexadecimal')
    if (objectIdString.startsWith("ObjectId('") && objectIdString.endsWith("')")) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    return objectIdString;
  }

  // =================== DEPARTMENT METHODS ===================

  /// Create new department
  static Future<Department?> createDepartment({
    required String name,
    required String description,
  }) async {
    try {
      if (_departmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Check if department already exists
      final existingDept = await _departmentsCollection!.findOne(where.eq('name', name));
      if (existingDept != null) {
        throw Exception('Khoa $name đã tồn tại');
      }

      final department = Department(
        name: name,
        description: description,
        doctorIds: [],
        createdAt: DateTime.now(),
      );

      final result = await _departmentsCollection!.insertOne(department.toJson());
      
      if (result.isSuccess) {
        final departmentId = result.id.toString();
        return department.copyWith(id: departmentId);
      } else {
        throw Exception('Lỗi tạo khoa');
      }
    } catch (e) {
      print('❌ Error creating department: $e');
      throw Exception('Lỗi tạo khoa: $e');
    }
  }

  /// Get all departments
  static Future<List<Department>> getAllDepartments() async {
    try {
      if (_departmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final cursor = _departmentsCollection!.find(where.eq('isActive', true));
      final results = await cursor.toList();
      
      return results.map((json) => Department.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting departments: $e');
      throw Exception('Lỗi lấy danh sách khoa: $e');
    }
  }

  /// Get department by ID
  static Future<Department?> getDepartmentById(String departmentId) async {
    try {
      if (_departmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(departmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      final result = await _departmentsCollection!.findOne(where.id(objectId));
      
      if (result != null) {
        return Department.fromJson(result);
      }
      return null;
    } catch (e) {
      print('❌ Error getting department by ID: $e');
      return null;
    }
  }

  // =================== DOCTOR METHODS ===================

  /// Create new doctor
  static Future<Doctor?> createDoctor({
    required String fullName,
    required String specialization,
    required String departmentId,
    required String title,
    required String experience,
    required String education,
    required String phone,
    required String email,
    required String description,
    String profileImage = '',
    required double consultationFee,
    required List<String> availableDays,
  }) async {
    try {
      if (_doctorsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Check if doctor email already exists
      final existingDoctor = await _doctorsCollection!.findOne(where.eq('email', email));
      if (existingDoctor != null) {
        throw Exception('Email $email đã được sử dụng');
      }

      final doctor = Doctor(
        fullName: fullName,
        specialization: specialization,
        departmentId: departmentId,
        title: title,
        experience: experience,
        education: education,
        phone: phone,
        email: email,
        description: description,
        profileImage: profileImage,
        consultationFee: consultationFee,
        availableDays: availableDays,
        createdAt: DateTime.now(),
      );

      final result = await _doctorsCollection!.insertOne(doctor.toJson());
      
      if (result.isSuccess) {
        final doctorId = result.id.toString();
        
        // Update department's doctorIds list
        await _updateDepartmentDoctorList(departmentId, doctorId, true);
        
        return doctor.copyWith(id: doctorId);
      } else {
        throw Exception('Lỗi tạo bác sĩ');
      }
    } catch (e) {
      print('❌ Error creating doctor: $e');
      throw Exception('Lỗi tạo bác sĩ: $e');
    }
  }

  /// Update department's doctor list
  static Future<void> _updateDepartmentDoctorList(String departmentId, String doctorId, bool add) async {
    try {
      String cleanId = _cleanObjectId(departmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      
      if (add) {
        await _departmentsCollection!.updateOne(
          where.id(objectId),
          modify.push('doctorIds', doctorId),
        );
      } else {
        await _departmentsCollection!.updateOne(
          where.id(objectId),
          modify.pull('doctorIds', doctorId),
        );
      }
    } catch (e) {
      print('❌ Error updating department doctor list: $e');
    }
  }

  /// Get doctors by department
  static Future<List<Doctor>> getDoctorsByDepartment(String departmentId) async {
    try {
      if (_doctorsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final cursor = _doctorsCollection!.find(
        where.eq('departmentId', departmentId).eq('isActive', true)
      );
      final results = await cursor.toList();
      
      return results.map((json) => Doctor.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting doctors by department: $e');
      throw Exception('Lỗi lấy danh sách bác sĩ: $e');
    }
  }

  /// Get doctor by ID
  static Future<Doctor?> getDoctorById(String doctorId) async {
    try {
      if (_doctorsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(doctorId);
      final objectId = ObjectId.fromHexString(cleanId);
      final result = await _doctorsCollection!.findOne(where.id(objectId));
      
      if (result != null) {
        return Doctor.fromJson(result);
      }
      return null;
    } catch (e) {
      print('❌ Error getting doctor by ID: $e');
      return null;
    }
  }

  // =================== SCHEDULE METHODS ===================

  /// Create doctor schedule
  static Future<DoctorSchedule?> createDoctorSchedule({
    required String doctorId,
    required String dayOfWeek,
    required String startTime,
    required String endTime,
    required String breakStartTime,
    required String breakEndTime,
    int slotDuration = 30,
    int maxPatientsPerSlot = 1,
  }) async {
    try {
      if (_schedulesCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Check if schedule already exists for this doctor on this day
      final existingSchedule = await _schedulesCollection!.findOne(
        where.eq('doctorId', doctorId).eq('dayOfWeek', dayOfWeek)
      );
      if (existingSchedule != null) {
        throw Exception('Lịch cho bác sĩ vào $dayOfWeek đã tồn tại');
      }

      final schedule = DoctorSchedule(
        doctorId: doctorId,
        dayOfWeek: dayOfWeek,
        startTime: startTime,
        endTime: endTime,
        breakStartTime: breakStartTime,
        breakEndTime: breakEndTime,
        slotDuration: slotDuration,
        maxPatientsPerSlot: maxPatientsPerSlot,
        createdAt: DateTime.now(),
      );

      final result = await _schedulesCollection!.insertOne(schedule.toJson());
      
      if (result.isSuccess) {
        final scheduleId = result.id.toString();
        return schedule.copyWith(id: scheduleId);
      } else {
        throw Exception('Lỗi tạo lịch khám');
      }
    } catch (e) {
      print('❌ Error creating doctor schedule: $e');
      throw Exception('Lỗi tạo lịch khám: $e');
    }
  }

  /// Get doctor schedules by doctor ID
  static Future<List<DoctorSchedule>> getDoctorSchedules(String doctorId) async {
    try {
      if (_schedulesCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final cursor = _schedulesCollection!.find(
        where.eq('doctorId', doctorId).eq('isActive', true)
      );
      final results = await cursor.toList();
      
      return results.map((json) => DoctorSchedule.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting doctor schedules: $e');
      throw Exception('Lỗi lấy lịch khám bác sĩ: $e');
    }
  }

  // =================== APPOINTMENT METHODS ===================

  /// Create new appointment
  static Future<Appointment?> createAppointment({
    required String patientId,
    required String doctorId,
    required String departmentId,
    required DateTime appointmentDate,
    required String appointmentTime,
    required String symptoms,
    required String notes,
    required double consultationFee,
  }) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Debug log
      print('🔍 DEBUG - AppointmentService.createAppointment:');
      print('  Patient ID: "$patientId"');
      print('  Doctor ID: "$doctorId"');
      print('  Department ID: "$departmentId"');
      print('  Date: $appointmentDate');
      print('  Time: $appointmentTime');

      // Check if slot is available
      final existingAppointment = await _appointmentsCollection!.findOne(
        where.eq('doctorId', doctorId)
             .eq('appointmentDate', appointmentDate.toIso8601String().split('T')[0])
             .eq('appointmentTime', appointmentTime)
             .ne('status', 'cancelled')
      );
      
      if (existingAppointment != null) {
        throw Exception('Khung giờ này đã được đặt');
      }

      final appointment = Appointment(
        patientId: patientId,
        doctorId: doctorId,
        departmentId: departmentId,
        appointmentDate: appointmentDate,
        appointmentTime: appointmentTime,
        symptoms: symptoms,
        notes: notes,
        consultationFee: consultationFee,
        createdAt: DateTime.now(),
      );

      print('🔍 DEBUG - Appointment object created:');
      print('  Appointment patientId: "${appointment.patientId}"');
      print('  JSON: ${appointment.toJson()}');

      final result = await _appointmentsCollection!.insertOne(appointment.toJson());
      
      if (result.isSuccess) {
        final appointmentId = result.id.toString();
        final createdAppointment = appointment.copyWith(id: appointmentId);
        
        print('🔍 DEBUG - Appointment saved successfully:');
        print('  Saved ID: $appointmentId');
        print('  Saved patientId: "${createdAppointment.patientId}"');
        
        return createdAppointment;
      } else {
        print('❌ DEBUG - Failed to save appointment');
        throw Exception('Lỗi đặt lịch khám');
      }
    } catch (e) {
      print('❌ Error creating appointment: $e');
      throw Exception('Lỗi đặt lịch khám: $e');
    }
  }

  /// Get appointments by patient ID
  static Future<List<Appointment>> getAppointmentsByPatient(String patientId) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      print('🔍 DEBUG - getAppointmentsByPatient:');
      print('  Searching for patientId: "$patientId"');

      final cursor = _appointmentsCollection!.find(
        where.eq('patientId', patientId)
      );
      
      final results = await cursor.toList();
      
      print('🔍 DEBUG - Query results: ${results.length} appointments found');
      for (int i = 0; i < results.length && i < 3; i++) {
        final result = results[i];
        print('  Result ${i + 1}: patientId="${result['patientId']}", date=${result['appointmentDate']}');
      }
      
      // Sort by appointmentDate descending in Dart
      results.sort((a, b) {
        final dateA = DateTime.parse(a['appointmentDate']);
        final dateB = DateTime.parse(b['appointmentDate']);
        return dateB.compareTo(dateA);
      });
      
      return results.map((json) => Appointment.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting appointments by patient: $e');
      throw Exception('Lỗi lấy lịch khám: $e');
    }
  }

  /// Get appointments by doctor ID
  static Future<List<Appointment>> getAppointmentsByDoctor(String doctorId) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final cursor = _appointmentsCollection!.find(
        where.eq('doctorId', doctorId)
      );
      
      final results = await cursor.toList();
      
      // Sort by appointmentDate ascending in Dart
      results.sort((a, b) {
        final dateA = DateTime.parse(a['appointmentDate']);
        final dateB = DateTime.parse(b['appointmentDate']);
        return dateA.compareTo(dateB);
      });
      
      return results.map((json) => Appointment.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting appointments by doctor: $e');
      throw Exception('Lỗi lấy lịch khám bác sĩ: $e');
    }
  }

  /// Update appointment status
  static Future<bool> updateAppointmentStatus(String appointmentId, AppointmentStatus status) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(appointmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      
      Map<String, dynamic> updates = {
        'status': status.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (status == AppointmentStatus.confirmed) {
        updates['confirmedAt'] = DateTime.now().toIso8601String();
      } else if (status == AppointmentStatus.completed) {
        updates['completedAt'] = DateTime.now().toIso8601String();
      }

      final result = await _appointmentsCollection!.updateOne(
        where.id(objectId),
        modify.set('status', updates['status'])
              .set('updatedAt', updates['updatedAt'])
      );

      return result.isSuccess;
    } catch (e) {
      print('❌ Error updating appointment status: $e');
      return false;
    }
  }

  /// Cancel appointment
  static Future<bool> cancelAppointment(String appointmentId, {String? reason}) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(appointmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      
      // Lấy thông tin lịch hẹn trước khi hủy
      final appointmentDoc = await _appointmentsCollection!.findOne(where.id(objectId));
      if (appointmentDoc == null) {
        throw Exception('Không tìm thấy lịch hẹn');
      }

      final appointment = Appointment.fromJson(appointmentDoc);
      
      // Kiểm tra có thể hủy không
      if (!appointment.canCancel) {
        throw Exception('Không thể hủy lịch hẹn này. Lịch hẹn phải được hủy ít nhất 2 giờ trước giờ khám.');
      }

      // Cập nhật trạng thái thành cancelled
      Map<String, dynamic> updates = {
        'status': AppointmentStatus.cancelled.toString().split('.').last,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      if (reason != null && reason.isNotEmpty) {
        updates['cancelledReason'] = reason;
      }

      final result = await _appointmentsCollection!.updateOne(
        where.id(objectId),
        modify
          .set('status', updates['status'])
          .set('updatedAt', updates['updatedAt'])
          .set('cancelledReason', reason ?? 'Bệnh nhân hủy lịch')
      );

      if (result.isSuccess) {
        print('✅ Appointment cancelled successfully: $appointmentId');
        return true;
      } else {
        print('❌ Failed to cancel appointment: $appointmentId');
        return false;
      }
    } catch (e) {
      print('❌ Error cancelling appointment: $e');
      throw Exception('Lỗi hủy lịch hẹn: $e');
    }
  }

  /// Test MongoDB connection
  static Future<bool> testConnection() async {
    try {
      if (await initialize()) {
        print('✅ Appointment Service connection test successful');
        return true;
      }
      return false;
    } catch (e) {
      print('❌ Appointment Service connection test failed: $e');
      return false;
    }
  }

  /// Get all appointments (for debugging)
  static Future<List<Appointment>> getAllAppointments() async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final cursor = _appointmentsCollection!.find();
      final results = await cursor.toList();
      
      return results.map((json) => Appointment.fromJson(json)).toList();
    } catch (e) {
      print('❌ Error getting all appointments: $e');
      throw Exception('Lỗi lấy tất cả lịch khám: $e');
    }
  }
<<<<<<< HEAD
  
  /// Update appointment payment status
    String appointmentId, 
    {required bool isPaid, required String paymentMethod}
  ) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(appointmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      
      // Lấy thông tin lịch hẹn trước khi cập nhật
      final appointmentDoc = await _appointmentsCollection!.findOne(where.id(objectId));
      if (appointmentDoc == null) {
        throw Exception('Không tìm thấy lịch hẹn');
      }

      // Cập nhật trạng thái thanh toán
      Map<String, dynamic> updates = {
        'isPaid': isPaid,
        'paymentMethod': paymentMethod,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      final result = await _appointmentsCollection!.updateOne(
        where.id(objectId),
        modify
          .set('isPaid', updates['isPaid'])
          .set('paymentMethod', updates['paymentMethod'])
          .set('updatedAt', updates['updatedAt'])
      );

      return result.isSuccess;
    } catch (e) {
      print('❌ Error updating payment status: $e');
      return false;
    }
  }

  /// Check appointment payment status
  static Future<Map<String, dynamic>> getAppointmentPaymentInfo(String appointmentId) async {
    try {
      if (_appointmentsCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      String cleanId = _cleanObjectId(appointmentId);
      final objectId = ObjectId.fromHexString(cleanId);
      
      // Lấy thông tin lịch hẹn
      final appointmentDoc = await _appointmentsCollection!.findOne(where.id(objectId));
      if (appointmentDoc == null) {
        throw Exception('Không tìm thấy lịch hẹn');
      }

      final appointment = Appointment.fromJson(appointmentDoc);
      
      return {
        'isPaid': appointment.isPaid,
        'paymentMethod': appointment.paymentMethod,
        'consultationFee': appointment.consultationFee,
      };
    } catch (e) {
      print('❌ Error getting payment info: $e');
      return {
        'error': e.toString(),
        'isPaid': false,
        'paymentMethod': null,
        'consultationFee': 0.0,
      };
    }
  }

  /// Get appointments collection (for other services)
  static DbCollection? getAppointmentsCollection() {
    return _appointmentsCollection;
  }

  /// Clean ObjectId format from string (public method)
  static String cleanObjectId(String objectIdString) {
    return _cleanObjectId(objectIdString);
  }
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
}
