import 'package:flutter/material.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../constants/app_constants.dart';
import 'vnpay_payment_screen.dart';
import 'my_appointments_screen.dart';

class PaymentMethodScreen extends StatelessWidget {
  final Appointment appointment;
  final Doctor doctor;
  final String doctorName;

  const PaymentMethodScreen({
    super.key,
    required this.appointment,
    required this.doctor,
    required this.doctorName,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chọn phương thức thanh toán'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAppointmentInfoCard(context),
            const SizedBox(height: 24),
            const Text(
              'Chọn phương thức thanh toán:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryBlue,
              ),
            ),
            const SizedBox(height: 16),
            _buildPaymentMethods(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentInfoCard(BuildContext context) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Lịch hẹn với $doctorName',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppConstants.primaryBlue,
              ),
            ),
            const Divider(),
            _buildInfoRow('Ngày khám', _formatDate(appointment.appointmentDate)),
            _buildInfoRow('Giờ khám', appointment.appointmentTime),
            _buildInfoRow('Phí khám', _formatCurrency(appointment.consultationFee)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Colors.black54,
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(BuildContext context) {
    return Column(
      children: [
        // VNPay
        _buildPaymentMethodCard(
          context,
          title: 'Thanh toán bằng VNPay',
          subtitle: 'Thanh toán trực tuyến qua VNPay',
          icon: 'assets/images/vnpay_icon.png',
          isIconAsset: true,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => VNPayPaymentScreen(
                  appointment: appointment,
                  doctor: doctor,
                  doctorName: doctorName,
                ),
              ),
            );
          },
        ),
        
        // Tiền mặt
        _buildPaymentMethodCard(
          context,
          title: 'Tiền mặt',
          subtitle: 'Thanh toán trực tiếp tại phòng khám',
          icon: Icons.money,
          onTap: () {
            _showCashPaymentDialog(context);
          },
        ),
        
        // Chuyển khoản ngân hàng
        _buildPaymentMethodCard(
          context,
          title: 'Chuyển khoản ngân hàng',
          subtitle: 'Thanh toán qua ngân hàng',
          icon: Icons.account_balance,
          onTap: () {
            _showBankTransferInfo(context);
          },
        ),
      ],
    );
  }

  Widget _buildPaymentMethodCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required dynamic icon,
    bool isIconAsset = false,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: isIconAsset
              ? Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Image.asset(
                    icon,
                    fit: BoxFit.contain,
                  ),
                )
              : Icon(
                  icon as IconData,
                  size: 30,
                  color: AppConstants.primaryBlue,
                ),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  void _showCashPaymentDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thanh toán tiền mặt'),
        content: const Text(
          'Vui lòng thanh toán trực tiếp tại quầy thu ngân của phòng khám trước khi khám.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyAppointmentsScreen(),
                ),
              );
            },
            child: const Text('Đã hiểu'),
          ),
        ],
      ),
    );
  }

  void _showBankTransferInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thông tin chuyển khoản'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Vui lòng chuyển khoản theo thông tin sau:'),
            const SizedBox(height: 16),
            _buildBankInfoRow('Ngân hàng', 'Vietcombank'),
            _buildBankInfoRow('Chủ tài khoản', 'CÔNG TY CP MEDIXCARE'),
            _buildBankInfoRow('Số tài khoản', '*************'),
            _buildBankInfoRow('Số tiền', _formatCurrency(appointment.consultationFee)),
            _buildBankInfoRow(
              'Nội dung CK',
              'MX${appointment.id != null ? appointment.id!.substring(0, 8) : "********"}',
            ),
            const SizedBox(height: 16),
            const Text(
              'Lưu ý: Vui lòng chuyển khoản trước khi đến khám và giữ lại biên lai để đối chiếu.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const MyAppointmentsScreen(),
                ),
              );
            },
            child: const Text('Đã hiểu'),
          ),
        ],
      ),
    );
  }

  Widget _buildBankInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0)} VNĐ';
  }
}
