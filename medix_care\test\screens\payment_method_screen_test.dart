import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/screens/payment_method_screen.dart';
import '../../lib/models/appointment.dart';
import '../../lib/models/doctor.dart';

void main() {
  group('PaymentMethodScreen Tests', () {
    late Appointment testAppointment;
    late Doctor testDoctor;

    setUp(() {
      testAppointment = Appointment(
        id: 'test-appointment-id',
        patientId: 'test-patient-id',
        doctorId: 'test-doctor-id',
        departmentId: 'test-department-id',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '09:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        status: AppointmentStatus.pending,
        consultationFee: 300000.0,
        isPaid: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testDoctor = Doctor(
        id: 'test-doctor-id',
        fullName: 'Dr. Test Doctor',
        specialization: 'Test Specialization',
        departmentId: 'test-department-id',
        title: '<PERSON><PERSON>ế<PERSON> sĩ',
        experience: '5 năm',
        education: '<PERSON><PERSON><PERSON> học <PERSON>',
        phone: '**********',
        email: '<EMAIL>',
        description: 'Test doctor description',
        consultationFee: 300000.0,
        availableDays: ['Monday', 'Tuesday', 'Wednesday'],
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    testWidgets('should display payment method screen correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Verify screen title
      expect(find.text('Chọn phương thức thanh toán'), findsOneWidget);
      
      // Verify appointment info card
      expect(find.text('Lịch hẹn với Dr. Test Doctor'), findsOneWidget);
      expect(find.text('300000 VNĐ'), findsOneWidget);
      
      // Verify payment methods section
      expect(find.text('Chọn phương thức thanh toán:'), findsOneWidget);
    });

    testWidgets('should display only one VNPay payment option', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Should find only one VNPay option (the streamlined one)
      expect(find.text('VNPay - Thanh toán trực tuyến'), findsOneWidget);
      expect(find.text('Thanh toán an toàn qua VNPay (Khuyến nghị)'), findsOneWidget);
      
      // Should not find the old VNPay option
      expect(find.text('Thanh toán bằng VNPay'), findsNothing);
      expect(find.text('VNPay - Thanh toán nhanh ⚡'), findsNothing);
    });

    testWidgets('should display all payment methods', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // VNPay option
      expect(find.text('VNPay - Thanh toán trực tuyến'), findsOneWidget);
      
      // Cash option
      expect(find.text('Tiền mặt'), findsOneWidget);
      expect(find.text('Thanh toán trực tiếp tại phòng khám'), findsOneWidget);
      
      // Bank transfer option
      expect(find.text('Chuyển khoản ngân hàng'), findsOneWidget);
      expect(find.text('Thanh toán qua ngân hàng'), findsOneWidget);
    });

    testWidgets('should show recommended badge for VNPay', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Should find the recommended badge
      expect(find.text('Khuyến nghị'), findsOneWidget);
    });

    testWidgets('should show cash payment dialog when cash option tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Tap on cash payment option
      await tester.tap(find.text('Tiền mặt'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.text('Thanh toán tiền mặt'), findsOneWidget);
      expect(find.text('Vui lòng thanh toán trực tiếp tại quầy thu ngân của phòng khám trước khi khám.'), findsOneWidget);
      expect(find.text('Đã hiểu'), findsOneWidget);
    });

    testWidgets('should show bank transfer dialog when bank option tapped', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Tap on bank transfer option
      await tester.tap(find.text('Chuyển khoản ngân hàng'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.text('Thông tin chuyển khoản'), findsOneWidget);
      expect(find.text('Vietcombank'), findsOneWidget);
      expect(find.text('CÔNG TY CP MEDIXCARE'), findsOneWidget);
      expect(find.text('*************'), findsOneWidget);
    });

    testWidgets('should format appointment date correctly', (WidgetTester tester) async {
      final specificDate = DateTime(2025, 8, 15);
      final appointmentWithSpecificDate = testAppointment.copyWith(
        appointmentDate: specificDate,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: appointmentWithSpecificDate,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Verify date formatting
      expect(find.text('15/8/2025'), findsOneWidget);
    });

    testWidgets('should format currency correctly', (WidgetTester tester) async {
      final appointmentWithDifferentFee = testAppointment.copyWith(
        consultationFee: 500000.0,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: appointmentWithDifferentFee,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Verify currency formatting
      expect(find.text('500000 VNĐ'), findsOneWidget);
    });

    testWidgets('should handle back navigation with confirmation', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Verify AppBar back button exists
      expect(find.byType(AppBar), findsOneWidget);
      
      // Note: Testing actual back navigation with confirmation dialog
      // would require more complex setup with navigation mocking
    });

    testWidgets('should display appointment time correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      // Verify appointment time is displayed
      expect(find.text('09:00'), findsOneWidget);
    });
  });
}
