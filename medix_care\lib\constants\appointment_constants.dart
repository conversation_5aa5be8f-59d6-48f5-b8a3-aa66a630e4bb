class AppointmentConstants {
  // Time slots
  static const List<String> timeSlots = [
    '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
    '13:00', '13:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30'
  ];

  // Days of week in English (for database)
  static const List<String> daysOfWeekEn = [
    'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'
  ];

  // Days of week in Vietnamese (for display)
  static const List<String> daysOfWeekVn = [
    'Thứ 2', 'Thứ 3', 'Th<PERSON> 4', 'Th<PERSON> 5', 'Th<PERSON> 6', 'Th<PERSON> 7', 'Ch<PERSON> nhật'
  ];

  // Appointment status in Vietnamese
  static const Map<String, String> appointmentStatusVn = {
    'pending': 'Chờ xác nhận',
    'confirmed': '<PERSON><PERSON> xác nhận',
    'completed': '<PERSON><PERSON> hoàn thành',
    'cancelled': 'Đã hủy',
    'noShow': 'Không đến khám',
  };

  // Gender options
  static const List<String> genderOptions = ['Nam', 'Nữ', 'Khác'];

  // Payment methods
  static const List<String> paymentMethods = [
    'Tiền mặt',
    'Chuyển khoản',
    'Thẻ tín dụng',
    'Ví điện tử',
  ];

  // Default values
  static const int defaultSlotDuration = 30; // minutes
  static const int maxPatientsPerSlot = 1;
  static const String defaultStartTime = '08:00';
  static const String defaultEndTime = '17:00';
  static const String defaultBreakStart = '12:00';
  static const String defaultBreakEnd = '13:00';

  // Validation
  static const int minBookingHoursAdvance = 2; // Minimum hours before appointment
  static const int maxBookingDaysAdvance = 30; // Maximum days to book in advance

  // Format patterns
  static const String dateFormat = 'dd/MM/yyyy';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'dd/MM/yyyy HH:mm';

  /// Convert English day to Vietnamese
  static String dayToVietnamese(String englishDay) {
    final index = daysOfWeekEn.indexOf(englishDay);
    return index != -1 ? daysOfWeekVn[index] : englishDay;
  }

  /// Convert Vietnamese day to English
  static String dayToEnglish(String vietnameseDay) {
    final index = daysOfWeekVn.indexOf(vietnameseDay);
    return index != -1 ? daysOfWeekEn[index] : vietnameseDay;
  }

  /// Get Vietnamese day of week from DateTime
  static String getDayOfWeek(DateTime date) {
    return daysOfWeekVn[date.weekday - 1];
  }

  /// Get English day of week from DateTime  
  static String getEnglishDayOfWeek(DateTime date) {
    return daysOfWeekEn[date.weekday - 1];
  }

  /// Check if appointment can be cancelled
  static bool canCancelAppointment(DateTime appointmentDateTime) {
    final now = DateTime.now();
    final hoursDifference = appointmentDateTime.difference(now).inHours;
    return hoursDifference >= minBookingHoursAdvance;
  }

  /// Check if appointment can be booked
  static bool canBookAppointment(DateTime appointmentDateTime) {
    final now = DateTime.now();
    final hoursDifference = appointmentDateTime.difference(now).inHours;
    final daysDifference = appointmentDateTime.difference(now).inDays;
    
    return hoursDifference >= minBookingHoursAdvance && 
           daysDifference <= maxBookingDaysAdvance;
  }

  /// Format currency (VNĐ)
  static String formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    )} VNĐ';
  }

  /// Format date to Vietnamese format
  static String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Parse time string to minutes since midnight
  static int timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Convert minutes since midnight to time string
  static String minutesToTime(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}';
  }

  /// Generate time slots between start and end time, excluding break time
  static List<String> generateTimeSlots({
    required String startTime,
    required String endTime,
    required String breakStartTime,
    required String breakEndTime,
    int slotDuration = defaultSlotDuration,
  }) {
    final startMinutes = timeToMinutes(startTime);
    final endMinutes = timeToMinutes(endTime);
    final breakStartMinutes = timeToMinutes(breakStartTime);
    final breakEndMinutes = timeToMinutes(breakEndTime);

    List<String> slots = [];
    int currentMinutes = startMinutes;

    while (currentMinutes < endMinutes) {
      // Skip break time
      if (currentMinutes < breakStartMinutes || currentMinutes >= breakEndMinutes) {
        slots.add(minutesToTime(currentMinutes));
      }
      currentMinutes += slotDuration;
    }

    return slots;
  }

  /// Get next available booking dates (excluding weekends if needed)
  static List<DateTime> getAvailableBookingDates({
    int days = 7,
    bool includeWeekends = true,
  }) {
    List<DateTime> dates = [];
    DateTime current = DateTime.now().add(const Duration(days: 1)); // Start from tomorrow

    while (dates.length < days) {
      if (includeWeekends || (current.weekday != DateTime.saturday && current.weekday != DateTime.sunday)) {
        dates.add(current);
      }
      current = current.add(const Duration(days: 1));
    }

    return dates;
  }

  /// Get formatted time range
  static String getTimeRange(String startTime, int durationMinutes) {
    final startMinutes = timeToMinutes(startTime);
    final endMinutes = startMinutes + durationMinutes;
    
    final endHours = endMinutes ~/ 60;
    final endMins = endMinutes % 60;
    
    return '$startTime - ${endHours.toString().padLeft(2, '0')}:${endMins.toString().padLeft(2, '0')}';
  }

  /// Check if a time is within business hours
  static bool isBusinessHour(String time) {
    final minutes = timeToMinutes(time);
    const morningStart = 8 * 60; // 08:00
    const morningEnd = 12 * 60;  // 12:00
    const afternoonStart = 14 * 60; // 14:00
    const afternoonEnd = 18 * 60;   // 18:00
    
    return (minutes >= morningStart && minutes < morningEnd) ||
           (minutes >= afternoonStart && minutes < afternoonEnd);
  }

  /// Get available days for filter dropdown
  static List<String> getAvailableDays() {
    return daysOfWeekVn;
  }

  /// Get common time slots for filter
  static List<String> getCommonTimeSlots() {
    return [
      '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
      '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'
    ];
  }
}
