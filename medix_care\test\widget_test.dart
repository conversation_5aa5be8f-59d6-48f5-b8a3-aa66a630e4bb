// Basic Flutter widget test for MedixCare app
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:medix_care/main.dart';

void main() {
  testWidgets('MedixCare app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const MyApp());

    // Verify that the app loads and shows the AppBar with app name
    expect(find.text('MedixCare'), findsAtLeastNWidgets(1));
    
    // Verify that app shows loading initially
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
    
    // Pump a few frames to let the app settle
    for (int i = 0; i < 5; i++) {
      await tester.pump(const Duration(milliseconds: 100));
    }
    
    // The app should have some basic structure now
    expect(find.byType(Scaffold), findsAtLeastNWidgets(1));
  });
}
