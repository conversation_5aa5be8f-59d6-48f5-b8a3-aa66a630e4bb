class Province {
  final String provinceCode;
  final String name;

  Province({
    required this.provinceCode,
    required this.name,
  });

  factory Province.fromJson(Map<String, dynamic> json) {
    return Province(
      provinceCode: json['province_code'] ?? '',
      name: json['name'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'province_code': provinceCode,
      'name': name,
    };
  }

  @override
  String toString() => name;
}
