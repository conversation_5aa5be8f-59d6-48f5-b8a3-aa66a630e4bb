import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/themes/app_bar_theme.dart' as custom_app_bar_theme;
import '../../lib/constants/app_constants.dart';

void main() {
  group('AppBarTheme Tests', () {
    testWidgets('create should return consistent AppBar', (WidgetTester tester) async {
      final appBar = custom_app_bar_theme.AppBarTheme.create(
        title: 'Test Title',
        centerTitle: true,
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Test Title');
      expect(appBar.backgroundColor, AppConstants.primaryBlue);
      expect(appBar.foregroundColor, AppConstants.whiteText);
      expect(appBar.centerTitle, true);
      expect(appBar.elevation, 2);
    });

    testWidgets('createPaymentAppBar should have security icon', (WidgetTester tester) async {
      final appBar = custom_app_bar_theme.AppBarTheme.createPaymentAppBar(
        title: 'Payment',
        showBackButton: true,
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Payment');
      expect(appBar.centerTitle, true);
      expect(appBar.elevation, 0);
      expect(appBar.actions, isNotNull);
      expect(appBar.actions!.length, 1);
    });

    testWidgets('createFormAppBar should handle save action', (WidgetTester tester) async {
      bool saveCalled = false;
      
      final appBar = custom_app_bar_theme.AppBarTheme.createFormAppBar(
        title: 'Form',
        onSave: () => saveCalled = true,
        hasUnsavedChanges: true,
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Form');
      expect(appBar.centerTitle, false);
      expect(appBar.actions, isNotNull);
      expect(appBar.actions!.length, 1);
      
      // Test save button
      final saveButton = appBar.actions!.first as TextButton;
      saveButton.onPressed!();
      expect(saveCalled, true);
    });

    testWidgets('createAuthAppBar should be centered', (WidgetTester tester) async {
      final appBar = custom_app_bar_theme.AppBarTheme.createAuthAppBar(
        title: 'Login',
        showBackButton: true,
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Login');
      expect(appBar.centerTitle, true);
      expect(appBar.elevation, 1);
    });

    testWidgets('createResultAppBar should have no back button', (WidgetTester tester) async {
      final appBar = custom_app_bar_theme.AppBarTheme.createResultAppBar(
        title: 'Result',
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Result');
      expect(appBar.centerTitle, true);
      expect(appBar.elevation, 0);
      expect(appBar.automaticallyImplyLeading, false);
    });

    testWidgets('createHomeAppBar should be left-aligned', (WidgetTester tester) async {
      final appBar = custom_app_bar_theme.AppBarTheme.createHomeAppBar(
        title: 'Home',
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Home');
      expect(appBar.centerTitle, false);
      expect(appBar.elevation, 2);
    });



    testWidgets('AppBarExtension should work with BuildContext', (WidgetTester tester) async {
      AppBar? createdAppBar;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              createdAppBar = context.createAppBar(
                title: 'Extension Test',
                centerTitle: true,
              );
              return Scaffold(
                appBar: createdAppBar,
                body: const Text('Test'),
              );
            },
          ),
        ),
      );

      expect(createdAppBar, isNotNull);
      expect(createdAppBar!.title, isA<Text>());
      expect((createdAppBar!.title as Text).data, 'Extension Test');
      expect(createdAppBar!.centerTitle, true);
    });

    testWidgets('createPaymentAppBar extension should work', (WidgetTester tester) async {
      AppBar? createdAppBar;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              createdAppBar = context.createPaymentAppBar(
                title: 'Payment Extension',
                showBackButton: false,
              );
              return Scaffold(
                appBar: createdAppBar,
                body: const Text('Test'),
              );
            },
          ),
        ),
      );

      expect(createdAppBar, isNotNull);
      expect(createdAppBar!.title, isA<Text>());
      expect((createdAppBar!.title as Text).data, 'Payment Extension');
      expect(createdAppBar!.centerTitle, true);
    });

    testWidgets('createFormAppBar extension should work', (WidgetTester tester) async {
      AppBar? createdAppBar;
      bool saveCalled = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              createdAppBar = context.createFormAppBar(
                title: 'Form Extension',
                onSave: () => saveCalled = true,
                hasUnsavedChanges: true,
              );
              return Scaffold(
                appBar: createdAppBar,
                body: const Text('Test'),
              );
            },
          ),
        ),
      );

      expect(createdAppBar, isNotNull);
      expect(createdAppBar!.title, isA<Text>());
      expect((createdAppBar!.title as Text).data, 'Form Extension');
      expect(createdAppBar!.actions, isNotNull);
      expect(createdAppBar!.actions!.length, 1);
    });

    testWidgets('createAuthAppBar extension should work', (WidgetTester tester) async {
      AppBar? createdAppBar;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) {
              createdAppBar = context.createAuthAppBar(
                title: 'Auth Extension',
                showBackButton: true,
              );
              return Scaffold(
                appBar: createdAppBar,
                body: const Text('Test'),
              );
            },
          ),
        ),
      );

      expect(createdAppBar, isNotNull);
      expect(createdAppBar!.title, isA<Text>());
      expect((createdAppBar!.title as Text).data, 'Auth Extension');
      expect(createdAppBar!.centerTitle, true);
    });
  });
}
