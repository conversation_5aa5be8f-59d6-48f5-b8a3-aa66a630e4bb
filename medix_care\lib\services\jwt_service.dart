import 'dart:convert';
import 'package:dart_jsonwebtoken/dart_jsonwebtoken.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user.dart';

class JWTService {
  // JWT Secret - trong production nên lưu trong environment variables
  static const String _jwtSecret = 'medixcare_jwt_secret_key_2024';
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  
  // Access token expiry: 15 minutes
  static const Duration _accessTokenExpiry = Duration(minutes: 15);
  
  // Refresh token expiry: 7 days
  static const Duration _refreshTokenExpiry = Duration(days: 7);

  /// Generate Access Token
  static String generateAccessToken(User user) {
    final jwt = JWT({
      'userId': user.id,
      'email': user.email,
      'fullName': user.fullName,
      'type': 'access',
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': DateTime.now().add(_accessTokenExpiry).millisecondsSinceEpoch ~/ 1000,
    });

    return jwt.sign(SecretKey(_jwtSecret));
  }

  /// Generate Refresh Token
  static String generateRefreshToken(User user) {
    final jwt = JWT({
      'userId': user.id,
      'email': user.email,
      'type': 'refresh',
      'iat': DateTime.now().millisecondsSinceEpoch ~/ 1000,
      'exp': DateTime.now().add(_refreshTokenExpiry).millisecondsSinceEpoch ~/ 1000,
    });

    return jwt.sign(SecretKey(_jwtSecret));
  }

  /// Verify Token
  static Map<String, dynamic>? verifyToken(String token) {
    try {
      final jwt = JWT.verify(token, SecretKey(_jwtSecret));
      return jwt.payload;
    } catch (e) {
      print('JWT verification error: $e');
      return null;
    }
  }

  /// Check if token is expired
  static bool isTokenExpired(String token) {
    try {
      final jwt = JWT.verify(token, SecretKey(_jwtSecret));
      final exp = jwt.payload['exp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      return now >= exp;
    } catch (e) {
      return true; // If error, consider expired
    }
  }

  /// Save tokens to local storage
  static Future<void> saveTokens(String accessToken, String refreshToken, User user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
    await prefs.setString(_userDataKey, jsonEncode(user.toJson()));
  }

  /// Get access token from local storage
  static Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  /// Get refresh token from local storage
  static Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  /// Get user data from local storage
  static Future<User?> getUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataString = prefs.getString(_userDataKey);
    if (userDataString != null) {
      try {
        final userData = jsonDecode(userDataString);
        return User.fromJson(userData);
      } catch (e) {
        print('Error parsing user data: $e');
        return null;
      }
    }
    return null;
  }

  /// Check if user is authenticated
  static Future<bool> isAuthenticated() async {
    try {
      final accessToken = await getAccessToken();
      if (accessToken == null) return false;

      // Check if access token is valid
      if (!isTokenExpired(accessToken)) {
        return true;
      }

      // Try to refresh token
      return await refreshAccessToken();
    } catch (e) {
      print('Error checking authentication: $e');
      // Don't logout on error, just return false
      return false;
    }
  }

  /// Refresh access token using refresh token
  static Future<bool> refreshAccessToken() async {
    try {
      final refreshToken = await getRefreshToken();
      if (refreshToken == null) return false;

      if (isTokenExpired(refreshToken)) {
        // Only logout if refresh token is actually expired
        await logout();
        return false;
      }

      // In a real app, this would call your backend API to refresh the token
      // For now, we'll generate a new token locally (not recommended for production)
      final userData = await getUserData();
      if (userData == null) return false;

      final newAccessToken = generateAccessToken(userData);
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_accessTokenKey, newAccessToken);

      return true;
    } catch (e) {
      // Don't logout on refresh error, just return false
      print('Error refreshing token: $e');
      return false;
    }
  }

  /// Logout - clear all tokens
  static Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userDataKey);
  }

  /// Get current user ID from token
  static Future<String?> getCurrentUserId() async {
    final accessToken = await getAccessToken();
    if (accessToken == null) return null;
    
    final payload = verifyToken(accessToken);
    return payload?['userId'];
  }

  /// Get current user from stored user data
  static Future<User?> getCurrentUser() async {
    try {
      final userData = await getUserData();
      return userData;
    } catch (e) {
      print('Error getting current user: $e');
      return null;
    }
  }

  /// Clear all tokens (alias for logout)
  static Future<void> clearTokens() async {
    await logout();
  }
}
