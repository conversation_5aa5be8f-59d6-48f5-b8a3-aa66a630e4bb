import 'package:flutter/material.dart';

/// App-wide constants
class AppConstants {
  // App Info
  static const String appName = 'MedixCare';
  static const String appSubtitle = 'Healthcare Registration System';
  static const String appTitle = 'Flutter Demo';
  
  // UI Text
  static const String registrationButton = 'Đăng ký tài khoản';
  
  // Messages
  static const String registrationSuccess = 'Đăng ký thành công!';
  static const String registrationFailed = 'Đăng ký thất bại';
  static const String cannotCreateAccount = 'Không thể tạo tài khoản';
  static const String usingDirectMongoDB = '🔄 Using Direct MongoDB Service';
  
  // Icons
  static const String hospitalIcon = '🏥';
  
  // Font Sizes
  static const double titleFontSize = 32.0;
  static const double subtitleFontSize = 16.0;
  static const double buttonFontSize = 18.0;
  
  // Spacing
  static const double paddingLarge = 40.0;
  static const double paddingMedium = 16.0;
  static const double paddingSmall = 8.0;
  static const double buttonVerticalPadding = 16.0;
  
  // Colors
  static const Color primaryBlue = Colors.blue;
  static const Color greyText = Colors.grey;
  static const Color whiteText = Colors.white;
  
  // Theme
  static const Color seedColor = Colors.deepPurple;
}

/// Authentication Service Configuration
class AuthConfig {
  static const bool useMockData = false;
  static const bool useDirectMongoDB = true;
}
