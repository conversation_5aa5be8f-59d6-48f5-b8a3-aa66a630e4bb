import 'package:flutter/material.dart';
import '../widgets/doctor_filter_widget.dart';

class FilterPopupDialog extends StatefulWidget {
  final DoctorFilter initialFilter;
  final Function(DoctorFilter) onFilterApplied;

  const FilterPopupDialog({
    super.key,
    required this.onFilterApplied,
    this.initialFilter = const DoctorFilter(),
  });
  late DoctorFilter _currentFilter;

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter;
  }

  void _onFilterChanged(DoctorFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
  }

  void _applyFilter() {
    widget.onFilterApplied(_currentFilter);
    Navigator.of(context).pop();
  }

  void _clearFilter() {
    setState(() {
      _currentFilter = const DoctorFilter();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(

                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),

                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Text(
                      'Bộ lọc tìm kiếm bác sĩ',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            
            // Filter Content
            Flexible(
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                  child: DoctorFilterWidget(
                    initialFilter: _currentFilter,
                    onFilterChanged: _onFilterChanged,
                    showTitle: false, // Hide title since we have header
                  ),
                ),
              ),
            ),
            
            // Action Buttons
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
<<<<<<< HEAD
                    color: Colors.grey.withValues(alpha: 0.3),

                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Clear Filter Button
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _currentFilter.isEmpty ? null : _clearFilter,
                      icon: const Icon(Icons.clear_all),
                      label: const Text('Xóa bộ lọc'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Apply Filter Button
                  Expanded(
                    flex: 2,
                    child: ElevatedButton.icon(
                      onPressed: _applyFilter,
                      icon: const Icon(Icons.check),
                      label: Text(
                        _currentFilter.isEmpty 
                          ? 'Hiển thị tất cả' 
                          : 'Áp dụng bộ lọc',
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Static method to show the filter dialog
class FilterDialog {
  static Future<DoctorFilter?> show({
    required BuildContext context,
    required DoctorFilter initialFilter,
  }) async {
    DoctorFilter? result;
    
    await showDialog<void>(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return FilterPopupDialog(
          initialFilter: initialFilter,
          onFilterApplied: (filter) {
            result = filter;
          },
        );
      },
    );
    
    return result;
  }
}
