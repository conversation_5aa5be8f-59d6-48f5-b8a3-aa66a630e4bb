import 'package:flutter/material.dart';
import '../services/appointment_service.dart';
import '../services/jwt_service.dart';

class DebugAppointmentsScreen extends StatefulWidget {
  const DebugAppointmentsScreen({super.key});

  @override
  State<DebugAppointmentsScreen> createState() => _DebugAppointmentsScreenState();
}

class _DebugAppointmentsScreenState extends State<DebugAppointmentsScreen> {
  String debugInfo = 'Đang tải...';
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _performDebugCheck();
  }

  Future<void> _performDebugCheck() async {
    setState(() {
      isLoading = true;
      debugInfo = 'Đang kiểm tra...';
    });

    StringBuffer buffer = StringBuffer();
    buffer.writeln('🔍 DEBUG - Kiểm tra Appointments');
    buffer.writeln('=' * 40);
    
    try {
      // Initialize services
      buffer.writeln('📡 Đang khởi tạo services...');
      await AppointmentService.initialize();
      buffer.writeln('✅ Services khởi tạo thành công');
      
      // Get current user
      buffer.writeln('\n👤 Kiểm tra user hiện tại...');
      final currentUser = await JWTService.getCurrentUser();
      if (currentUser != null) {
        buffer.writeln('✅ User hiện tại:');
        buffer.writeln('   ID: ${currentUser.id}');
        buffer.writeln('   Name: ${currentUser.fullName}');
        buffer.writeln('   Email: ${currentUser.email}');
      } else {
        buffer.writeln('❌ Không có user nào đang đăng nhập');
      }
      
      // Get all appointments
      buffer.writeln('\n📋 Kiểm tra tất cả appointments...');
      final allAppointments = await AppointmentService.getAllAppointments();
      buffer.writeln('📊 Tổng số appointments: ${allAppointments.length}');
      
      if (allAppointments.isEmpty) {
        buffer.writeln('❌ Không có appointment nào!');
      } else {
        buffer.writeln('\n📝 Danh sách appointments:');
        for (int i = 0; i < allAppointments.length && i < 5; i++) {
          final app = allAppointments[i];
          final patientIdShort = app.patientId.length > 8 ? app.patientId.substring(0, 8) : app.patientId;
          final doctorIdShort = app.doctorId.length > 8 ? app.doctorId.substring(0, 8) : app.doctorId;
          
          buffer.writeln('   ${i + 1}. ID: ${app.id?.substring(0, 8) ?? 'N/A'}');
          buffer.writeln('      Patient ID: "$patientIdShort"');
          buffer.writeln('      Doctor ID: "$doctorIdShort"');
          buffer.writeln('      Date: ${app.appointmentDate}');
          buffer.writeln('      Time: ${app.appointmentTime}');
          buffer.writeln('      Status: ${app.status}');
          buffer.writeln('      ---');
        }
        if (allAppointments.length > 5) {
          buffer.writeln('   ... và ${allAppointments.length - 5} appointments khác');
        }
      }
      
      // Check user appointments
      if (currentUser != null) {
        buffer.writeln('\n🎯 Appointments của user này...');
        final userAppointments = await AppointmentService.getAppointmentsByPatient(currentUser.id!);
        buffer.writeln('📊 Số lượng: ${userAppointments.length}');
        
        if (userAppointments.isEmpty) {
          buffer.writeln('❌ User này chưa có appointment nào!');
          
          // Find similar patientIds
          buffer.writeln('\n🔍 Tìm appointments có patientId tương tự...');
          bool foundSimilar = false;
          for (final app in allAppointments) {
            final userIdStr = currentUser.id!;
            final patientIdStr = app.patientId;
            
            if (userIdStr.trim() != patientIdStr.trim()) {
              buffer.writeln('🔸 So sánh:');
              buffer.writeln('   User ID: "$userIdStr" (length: ${userIdStr.length})');
              buffer.writeln('   Patient ID: "$patientIdStr" (length: ${patientIdStr.length})');
              buffer.writeln('   Equals: ${userIdStr == patientIdStr}');
              buffer.writeln('   Trimmed equals: ${userIdStr.trim() == patientIdStr.trim()}');
              foundSimilar = true;
              break;
            }
          }
          if (!foundSimilar) {
            buffer.writeln('   Không tìm thấy appointments nào');
          }
        } else {
          buffer.writeln('✅ Tìm thấy ${userAppointments.length} appointments');
        }
      }
      
    } catch (e) {
      buffer.writeln('❌ Lỗi: $e');
    }
    
    buffer.writeln('\n✅ Debug hoàn thành!');
    
    setState(() {
      debugInfo = buffer.toString();
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Appointments'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _performDebugCheck,
          ),
        ],
      ),
      body: isLoading
        ? const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Đang kiểm tra...'),
              ],
            ),
          )
        : SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                debugInfo,
                style: const TextStyle(
                  color: Colors.white,
                  fontFamily: 'Courier',
                  fontSize: 12,
                ),
              ),
            ),
          ),
    );
  }
}
