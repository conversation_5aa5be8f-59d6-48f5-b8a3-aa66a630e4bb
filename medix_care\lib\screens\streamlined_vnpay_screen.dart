import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../services/vnpay_service.dart';
import '../constants/app_constants.dart';
import '../constants/color_constants.dart';
import '../themes/app_bar_theme.dart' as custom_app_bar_theme;
import '../utils/back_navigation_helper.dart';
import 'payment_result_screen.dart';

class StreamlinedVNPayScreen extends StatefulWidget {
  final Appointment appointment;
  final Doctor doctor;
  final bool useWebView;

  const StreamlinedVNPayScreen({
    super.key,
    required this.appointment,
    required this.doctor,
    this.useWebView = true,
  });

  @override
  State<StreamlinedVNPayScreen> createState() => _StreamlinedVNPayScreenState();
}

class _StreamlinedVNPayScreenState extends State<StreamlinedVNPayScreen>
    with TickerProviderStateMixin {
  bool _isLoading = false;
  bool _isPaymentProcessing = false;
  String? _errorMessage;
  String? _paymentUrl;
  late WebViewController _webViewController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _initializePayment() async {
    setState(() {
      _isLoading = true;
      _isPaymentProcessing = true;
      _errorMessage = null;
    });

    try {
      // Lấy địa chỉ IP của thiết bị
      final ipAddress = await _getIpAddress();
      
      // Tạo URL thanh toán VNPay
      final paymentUrl = await VNPayService.createPaymentUrl(
        widget.appointment,
        ipAddress,
        locale: 'vn',
      );
      
      setState(() {
        _paymentUrl = paymentUrl;
        _isLoading = false;
      });

      if (widget.useWebView) {
        _setupWebView();
      } else {
        await _openExternalBrowser();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isPaymentProcessing = false;
        _errorMessage = 'Lỗi khởi tạo thanh toán: ${e.toString()}';
      });
    }
  }

  void _setupWebView() {
    if (_paymentUrl != null) {
      _webViewController = WebViewController()
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: (String url) {
              if (url.contains('payment-result') || url.contains(_getReturnUrlPattern())) {
                _handlePaymentResult(url);
              }
            },
            onPageFinished: (String url) {
              setState(() {
                _isPaymentProcessing = false;
              });
            },
            onWebResourceError: (WebResourceError error) {
              setState(() {
                _isPaymentProcessing = false;
                _errorMessage = 'Lỗi tải trang thanh toán: ${error.description}';
              });
            },
          ),
        )
        ..loadRequest(Uri.parse(_paymentUrl!));
    }
  }

  Future<void> _openExternalBrowser() async {
    if (_paymentUrl != null) {
      final opened = await VNPayService.openPaymentUrl(_paymentUrl!);
      if (!opened) {
        setState(() {
          _errorMessage = 'Không thể mở trang thanh toán VNPay';
          _isPaymentProcessing = false;
        });
      } else {
        // Simulate waiting for payment result
        await Future.delayed(const Duration(seconds: 2));
        setState(() {
          _isPaymentProcessing = false;
        });
      }
    }
  }

  String _getReturnUrlPattern() {
    return 'medixcare.com/payment-result';
  }

  Future<String> _getIpAddress() async {
    try {
      // Trong môi trường thực tế, sử dụng một API để lấy IP công khai
      return '127.0.0.1';
    } catch (e) {
      return '127.0.0.1';
    }
  }

  void _handlePaymentResult(String url) {
    // Parse query parameters từ URL
    final uri = Uri.parse(url);
    final params = uri.queryParameters;
    
    // Kiểm tra kết quả thanh toán từ VNPay
    final responseCode = params['vnp_ResponseCode'];
    final status = responseCode == '00'; // 00 = thành công
    
    // Chuyển sang màn hình kết quả thanh toán
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PaymentResultScreen(
          appointment: widget.appointment,
          doctor: widget.doctor,
          status: status,
          responseCode: responseCode,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BackNavigationHelper.createPaymentWillPopScope(
      isPaymentInProgress: _isPaymentProcessing,
      child: Scaffold(
        backgroundColor: ColorConstants.backgroundLight,
        appBar: custom_app_bar_theme.AppBarTheme.createPaymentAppBar(
          title: 'Thanh toán VNPay',
          onBackPressed: () => _handleBackNavigation(context),
          showBackButton: !_isPaymentProcessing,
        ),
        body: FadeTransition(
          opacity: _fadeAnimation,
          child: _buildBody(),
        ),
      ),
    );
  }

  /// Handle back navigation with payment state check
  void _handleBackNavigation(BuildContext context) async {
    if (!mounted) return;

    if (_isPaymentProcessing) {
      final navigator = Navigator.of(context);
      final shouldExit = await BackNavigationHelper.showBackConfirmationDialog(
        context,
        title: 'Cảnh báo',
        message: 'Quá trình thanh toán đang diễn ra. Thoát ngay bây giờ có thể làm mất giao dịch. Bạn có chắc chắn muốn thoát?',
        confirmText: 'Vẫn thoát',
        cancelText: 'Tiếp tục thanh toán',
      );

      if (shouldExit && mounted) {
        navigator.pop();
      }
    } else {
      BackNavigationHelper.safeNavigateBack(context);
    }
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_paymentUrl == null) {
      return _buildInitialState();
    }

    if (widget.useWebView) {
      return _buildWebViewState();
    } else {
      return _buildExternalBrowserState();
    }
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.primaryMedium),
                  strokeWidth: 3,
                ),
                const SizedBox(height: 16),
                Text(
                  'Đang khởi tạo thanh toán...',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: ColorConstants.textPrimaryMedium,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorConstants.errorMedium,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Có lỗi xảy ra',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimaryHigh,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _errorMessage!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorConstants.textPrimaryLow,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.buttonSecondaryMedium,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Quay lại'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _initializePayment,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorConstants.primaryMedium,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Thử lại'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInitialState() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildPaymentInfoCard(),
          const SizedBox(height: 24),
          _buildVNPayBrandingCard(),
          const SizedBox(height: 24),
          _buildPaymentInstructions(),
          const SizedBox(height: 32),
          _buildPaymentButton(),
        ],
      ),
    );
  }

  Widget _buildWebViewState() {
    return Column(
      children: [
        if (_isPaymentProcessing)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            color: ColorConstants.infoLow,
            child: Row(
              children: [
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(ColorConstants.infoMedium),
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Đang tải trang thanh toán...',
                  style: TextStyle(
                    color: ColorConstants.infoHigh,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        Expanded(
          child: WebViewWidget(controller: _webViewController),
        ),
      ],
    );
  }

  Widget _buildExternalBrowserState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.open_in_browser,
                    size: 64,
                    color: ColorConstants.primaryMedium,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Thanh toán VNPay',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: ColorConstants.textPrimaryHigh,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Trang thanh toán đã được mở trong trình duyệt. Vui lòng hoàn tất thanh toán và quay lại ứng dụng.',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      color: ColorConstants.textPrimaryLow,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorConstants.primaryMedium,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text('Quay lại'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: ColorConstants.primaryMedium,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Thông tin thanh toán',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimaryHigh,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: ColorConstants.backgroundLight,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: ColorConstants.borderLight,
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  _buildInfoRow('Bác sĩ', '${widget.doctor.title} ${widget.doctor.fullName}'),
                  const SizedBox(height: 12),
                  _buildInfoRow('Chuyên khoa', widget.doctor.specialization),
                  const SizedBox(height: 12),
                  _buildInfoRow('Ngày khám', _formatDate(widget.appointment.appointmentDate)),
                  const SizedBox(height: 12),
                  _buildInfoRow('Giờ khám', widget.appointment.appointmentTime),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: ColorConstants.primaryLow.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: ColorConstants.primaryLow,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Tổng phí khám:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: ColorConstants.textPrimaryHigh,
                          ),
                        ),
                        Text(
                          _formatCurrency(widget.appointment.consultationFee),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: ColorConstants.primaryMedium,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: TextStyle(
              color: ColorConstants.textPrimaryLow,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: ColorConstants.textPrimaryHigh,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildVNPayBrandingCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: const EdgeInsets.all(20.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              const Color(0xFF1E88E5),
              const Color(0xFF1976D2),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            // VNPay Logo placeholder - using text for now since no asset exists
            Container(
              height: 60,
              width: 120,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Center(
                child: Text(
                  'VNPay',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1976D2),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Thanh toán an toàn & bảo mật',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Được bảo vệ bởi công nghệ mã hóa SSL 256-bit',
              style: TextStyle(
                fontSize: 12,
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInstructions() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: ColorConstants.infoMedium,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Hướng dẫn thanh toán',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorConstants.textPrimaryHigh,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInstructionStep(
              '1',
              'Nhấn nút "Thanh toán ngay" bên dưới',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              '2',
              'Chọn phương thức thanh toán (ATM, QR Code, Ví điện tử)',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              '3',
              'Nhập thông tin thanh toán và xác nhận',
            ),
            const SizedBox(height: 12),
            _buildInstructionStep(
              '4',
              'Hoàn tất thanh toán và nhận kết quả',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: ColorConstants.warningLow.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: ColorConstants.warningMedium,
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.security,
                    color: ColorConstants.warningHigh,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Giao dịch được bảo mật bởi VNPay với công nghệ mã hóa SSL',
                      style: TextStyle(
                        fontSize: 12,
                        color: ColorConstants.warningHigh,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionStep(String step, String instruction) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: ColorConstants.primaryMedium,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              step,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            instruction,
            style: TextStyle(
              fontSize: 14,
              color: ColorConstants.textPrimaryMedium,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        gradient: LinearGradient(
          colors: [
            ColorConstants.primaryMedium,
            ColorConstants.primaryHigh,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: ColorConstants.primaryMedium.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: _isPaymentProcessing ? null : _initializePayment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.transparent,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isPaymentProcessing
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Text(
                    'Đang xử lý...',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.payment,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Thanh toán ngay',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final dayName = _getDayName(date.weekday);
    final dateStr = '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    return '$dayName, $dateStr';
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Thứ 2';
      case 2: return 'Thứ 3';
      case 3: return 'Thứ 4';
      case 4: return 'Thứ 5';
      case 5: return 'Thứ 6';
      case 6: return 'Thứ 7';
      case 7: return 'Chủ nhật';
      default: return '';
    }
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    )} VNĐ';
  }
}
