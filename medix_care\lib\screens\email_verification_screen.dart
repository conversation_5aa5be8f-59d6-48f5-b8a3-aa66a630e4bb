import 'package:flutter/material.dart';
import 'dart:async';
import '../constants/app_constants.dart';
import '../services/email_service.dart';
import '../services/mongodb_direct_service.dart';
import '../utils/validation_helper.dart';
import 'login_screen.dart';

class EmailVerificationScreen extends StatefulWidget {
  final String email;
  final String userName;
  final String userId;
  final VoidCallback? onVerificationSuccess;

  const EmailVerificationScreen({
    super.key,
    required this.email,
    required this.userName,
    required this.userId,
    this.onVerificationSuccess,
  });

  @override
  State<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  
  String? _storedVerificationCode;
  bool _isLoading = false;
  bool _isResending = false;
  int _countdown = 60;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    print('🔄 EmailVerificationScreen initiated for: ${widget.email}');
    print('🔄 User: ${widget.userName}, ID: ${widget.userId}');
    _sendVerificationCode();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _sendVerificationCode() async {
    print('🚀 Starting verification code send process...');
    setState(() {
      _isResending = true;
    });

    try {
      // Initialize MongoDB connection if not already done
      print('🔄 Ensuring MongoDB connection...');
      await MongoDBDirectService.initialize();
      print('✅ MongoDB connection ensured');
      
      // Generate verification code
      _storedVerificationCode = EmailService.generateVerificationCode();
      print('🔢 Generated verification code: $_storedVerificationCode');
      
      // Send email
      print('📧 Attempting to send email to: ${widget.email}');
      final success = await EmailService.sendVerificationEmail(
        toEmail: widget.email,
        userName: widget.userName,
        verificationCode: _storedVerificationCode!,
      );
      print('📧 Email send result: $success');

      if (success) {
        // Store verification code in MongoDB with expiry
        await MongoDBDirectService.updateUser(widget.userId, {
          'verificationCode': _storedVerificationCode,
          'verificationCodeExpiry': DateTime.now().add(const Duration(minutes: 10)).toIso8601String(),
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Mã xác thực đã được gửi đến ${widget.email}'),
              backgroundColor: Colors.green,
            ),
          );
          _startCountdown();
        }
      } else {
        throw Exception('Không thể gửi email xác thực');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi gửi email: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isResending = false;
        });
      }
    }
  }

  void _startCountdown() {
    setState(() {
      _countdown = 60;
    });
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _verifyCode() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize MongoDB connection if not already done
      print('🔄 Ensuring MongoDB connection for verification...');
      await MongoDBDirectService.initialize();
      print('✅ MongoDB connection ensured for verification');
      
      final enteredCode = _codeController.text.trim();
      
      // Get stored verification code from MongoDB
      final user = await MongoDBDirectService.getUserById(widget.userId);
      if (user == null) {
        throw Exception('Không tìm thấy thông tin người dùng');
      }

      // Check if verification code matches and not expired
      // Note: In real implementation, you should get these from user document
      if (_storedVerificationCode == null) {
        throw Exception('Mã xác thực đã hết hạn. Vui lòng gửi lại mã mới.');
      }

      if (enteredCode != _storedVerificationCode) {
        throw Exception('Mã xác thực không đúng');
      }

      // Update user as email verified
      final success = await MongoDBDirectService.updateUser(widget.userId, {
        'isEmailVerified': true,
        'verificationCode': null,
        'verificationCodeExpiry': null,
        'emailVerifiedAt': DateTime.now().toIso8601String(),
      });

      if (success) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Xác thực email thành công!'),
              backgroundColor: Colors.green,
            ),
          );

          // Call success callback or navigate
          if (widget.onVerificationSuccess != null) {
            widget.onVerificationSuccess!();
          } else {
            // Navigate to login screen
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const LoginScreen()),
              (route) => false,
            );
          }
        }
      } else {
        throw Exception('Không thể cập nhật trạng thái xác thực');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi xác thực: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Xác thực email'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Header
                Icon(
                  Icons.mark_email_read,
                  size: 80,
                  color: AppConstants.primaryBlue,
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                Text(
                  'Xác thực email',
                  style: const TextStyle(
                    fontSize: AppConstants.titleFontSize,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryBlue,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                Text(
                  'Chúng tôi đã gửi mã xác thực 6 chữ số đến:',
                  style: const TextStyle(
                    fontSize: AppConstants.subtitleFontSize,
                    color: AppConstants.greyText,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.paddingSmall),
                
                Text(
                  widget.email,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryBlue,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Verification code input
                TextFormField(
                  controller: _codeController,
                  keyboardType: TextInputType.number,
                  textAlign: TextAlign.center,
                  maxLength: 6,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 8,
                  ),
                  decoration: const InputDecoration(
                    labelText: 'Mã xác thực',
                    hintText: '000000',
                    prefixIcon: Icon(Icons.security),
                    border: OutlineInputBorder(),
                    counterText: '',
                  ),
                  validator: (value) => ValidationHelper.getVerificationCodeValidationError(value ?? ''),
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Verify button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _verifyCode,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryBlue,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: AppConstants.whiteText)
                        : const Text(
                            'Xác thực',
                            style: TextStyle(
                              color: AppConstants.whiteText,
                              fontSize: AppConstants.buttonFontSize,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Resend code
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text(
                      'Không nhận được mã? ',
                      style: TextStyle(color: AppConstants.greyText),
                    ),
                    TextButton(
                      onPressed: (_countdown > 0 || _isResending) ? null : _sendVerificationCode,
                      child: _isResending
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : Text(
                              _countdown > 0 
                                  ? 'Gửi lại (${_countdown}s)'
                                  : 'Gửi lại mã',
                              style: TextStyle(
                                color: _countdown > 0 
                                    ? AppConstants.greyText 
                                    : AppConstants.primaryBlue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Instructions
                Container(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '📧 Hướng dẫn:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                      ),
                      SizedBox(height: 8),
                      Text('• Kiểm tra hộp thư đến và thư mục spam'),
                      Text('• Mã xác thực có hiệu lực trong 10 phút'),
                      Text('• Nếu không nhận được, bấm "Gửi lại mã"'),
                      Text('• Liên hệ hỗ trợ nếu vẫn gặp vấn đề'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
