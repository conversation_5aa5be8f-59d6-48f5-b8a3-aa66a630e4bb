import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../lib/screens/home_screen.dart';
import '../../lib/screens/my_appointments_screen.dart';
import '../../lib/screens/book_appointment_screen.dart';
import '../../lib/screens/payment_method_screen.dart';
import '../../lib/models/user.dart';
import '../../lib/models/appointment.dart';
import '../../lib/models/doctor.dart';
import '../../lib/models/department.dart';
import '../../lib/services/jwt_service.dart';

void main() {
  group('Navigation Integration Tests', () {
    late User testUser;
    late Doctor testDoctor;
    late Department testDepartment;
    late Appointment testAppointment;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      testUser = User(
        id: 'test-user-id',
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '0123456789',
        gender: 'Nam',
        birthYear: 1990,
        province: 'Hà Nội',
        provinceCode: '01',
        ward: 'Ba Đình',
        wardCode: '001',
        isEmailVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testDoctor = Doctor(
        id: 'test-doctor-id',
        fullName: 'Dr. Test Doctor',
        specialization: 'Test Specialization',
        departmentId: 'test-department-id',
        title: 'Tiến sĩ',
        experience: '5 năm',
        education: 'Đại học Y Hà Nội',
        phone: '0123456789',
        email: '<EMAIL>',
        description: 'Test doctor description',
        consultationFee: 300000.0,
        availableDays: ['Monday', 'Tuesday', 'Wednesday'],
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testDepartment = Department(
        id: 'test-department-id',
        name: 'Test Department',
        description: 'Test department description',
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      testAppointment = Appointment(
        id: 'test-appointment-id',
        patientId: 'test-user-id',
        doctorId: 'test-doctor-id',
        departmentId: 'test-department-id',
        appointmentDate: DateTime.now().add(const Duration(days: 1)),
        appointmentTime: '09:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        status: AppointmentStatus.pending,
        consultationFee: 300000.0,
        isPaid: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Setup authenticated user
      final accessToken = JWTService.generateAccessToken(testUser);
      final refreshToken = JWTService.generateRefreshToken(testUser);
      await JWTService.saveTokens(accessToken, refreshToken, testUser);
    });

    testWidgets('should navigate from Home to MyAppointments without logout', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      // Wait for home screen to load
      await tester.pumpAndSettle();

      // Open drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Tap on "Lịch khám của tôi"
      await tester.tap(find.text('Lịch khám của tôi'));
      await tester.pumpAndSettle();

      // Should navigate to MyAppointmentsScreen without issues
      expect(find.byType(MyAppointmentsScreen), findsOneWidget);
    });

    testWidgets('should handle back navigation from MyAppointments correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const HomeScreen(),
          routes: {
            '/my_appointments': (context) => const MyAppointmentsScreen(),
          },
        ),
      );

      // Navigate to MyAppointments
      await tester.pumpAndSettle();
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Lịch khám của tôi'));
      await tester.pumpAndSettle();

      // Should be on MyAppointmentsScreen
      expect(find.byType(MyAppointmentsScreen), findsOneWidget);

      // Navigate back using back button
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Should be back on HomeScreen, not logged out
      expect(find.byType(HomeScreen), findsOneWidget);
    });

    testWidgets('should navigate from BookAppointment to Payment without pushReplacement issues', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BookAppointmentScreen(
            doctor: testDoctor,
            department: testDepartment,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // The screen should load without issues
      expect(find.byType(BookAppointmentScreen), findsOneWidget);
      
      // Note: Full booking flow test would require mocking services
      // This test verifies the screen loads and doesn't crash
    });

    testWidgets('should handle PaymentMethodScreen navigation correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: PaymentMethodScreen(
            appointment: testAppointment,
            doctor: testDoctor,
            doctorName: testDoctor.fullName,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Should display payment method screen
      expect(find.text('Chọn phương thức thanh toán'), findsOneWidget);
      expect(find.text('VNPay - Thanh toán trực tuyến'), findsOneWidget);
      
      // Should have back navigation
      expect(find.byIcon(Icons.arrow_back), findsOneWidget);
    });

    testWidgets('should preserve navigation stack during payment flow', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const HomeScreen(),
          routes: {
            '/my_appointments': (context) => const MyAppointmentsScreen(),
            '/payment_method': (context) => PaymentMethodScreen(
              appointment: testAppointment,
              doctor: testDoctor,
              doctorName: testDoctor.fullName,
            ),
          },
        ),
      );

      // Start from home
      await tester.pumpAndSettle();
      
      // Navigate to MyAppointments
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Lịch khám của tôi'));
      await tester.pumpAndSettle();

      // Should be on MyAppointments
      expect(find.byType(MyAppointmentsScreen), findsOneWidget);

      // Navigate to PaymentMethod (simulated)
      Navigator.of(tester.element(find.byType(MyAppointmentsScreen))).pushNamed('/payment_method');
      await tester.pumpAndSettle();

      // Should be on PaymentMethod
      expect(find.byType(PaymentMethodScreen), findsOneWidget);

      // Navigate back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Should be back on MyAppointments, not logged out or crashed
      expect(find.byType(MyAppointmentsScreen), findsOneWidget);
    });

    testWidgets('should handle authentication expiry gracefully during navigation', (WidgetTester tester) async {
      // Clear tokens to simulate expired authentication
      await JWTService.logout();

      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Try to navigate to authenticated screen
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Should still show drawer options, but authentication will be checked during actual navigation
      expect(find.text('Đăng nhập'), findsOneWidget);
      expect(find.text('Đăng ký'), findsOneWidget);
    });

    testWidgets('should not crash when navigating with invalid navigation stack', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () {
                  // Try to pop when there's nothing to pop
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Cannot pop')),
                    );
                  }
                },
                child: const Text('Try Pop'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Try Pop'));
      await tester.pumpAndSettle();

      // Should show snackbar instead of crashing
      expect(find.text('Cannot pop'), findsOneWidget);
    });

    testWidgets('should maintain user session across navigation', (WidgetTester tester) async {
      // Setup authenticated user
      final accessToken = JWTService.generateAccessToken(testUser);
      final refreshToken = JWTService.generateRefreshToken(testUser);
      await JWTService.saveTokens(accessToken, refreshToken, testUser);

      await tester.pumpWidget(
        const MaterialApp(
          home: HomeScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Should show authenticated user info
      expect(find.text('Xin chào, ${testUser.fullName}'), findsOneWidget);

      // Navigate to appointments
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Lịch khám của tôi'));
      await tester.pumpAndSettle();

      // Navigate back
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Should still show authenticated user info (session maintained)
      expect(find.text('Xin chào, ${testUser.fullName}'), findsOneWidget);
    });
  });
}
