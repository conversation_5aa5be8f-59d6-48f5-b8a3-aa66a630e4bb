import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/string_constants.dart';
import '../constants/numeric_constants.dart';
import '../constants/color_constants.dart';
import '../constants/spacing_constants.dart';
import '../constants/size_constants.dart';
import '../models/province.dart';
import '../models/ward.dart';
import '../services/location_service.dart';
import '../themes/app_bar_theme.dart' as custom_app_bar_theme;
import '../utils/back_navigation_helper.dart';
import 'set_password_screen.dart';

class RegistrationScreen extends StatefulWidget {
  const RegistrationScreen({
    super.key,
  });

  @override
  State<RegistrationScreen> createState() => _RegistrationScreenState();
}

class _RegistrationScreenState extends State<RegistrationScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Controllers for text fields
  final _fullNameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  
  // Selected values
  String? _selectedGender;
  int? _selectedBirthYear;
  Province? _selectedProvince;
  Ward? _selectedWard;
  
  // Lists for dropdown data
  List<Province> _provinces = [];
  List<Ward> _wards = [];
  
  // Loading states
  bool _isLoadingProvinces = false;
  bool _isLoadingWards = false;
  
  // Gender options
  final List<String> _genderOptions = [
    StringConstants.genderMale,
    StringConstants.genderFemale,
    StringConstants.genderOther,
  ];
  
  // Birth years list
  late List<int> _birthYears;
  
  @override
  void initState() {
    super.initState();
    _birthYears = List.generate(
      NumericConstants.maxBirthYear - NumericConstants.minBirthYear + 1,
      (index) => NumericConstants.maxBirthYear - index,
    );
    _loadProvinces();
  }
  
  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }
  
  // Load provinces from API
  Future<void> _loadProvinces() async {
    setState(() {
      _isLoadingProvinces = true;
    });
    
    try {
      final provinces = await LocationService.getProvinces();
      setState(() {
        _provinces = provinces;
        _isLoadingProvinces = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProvinces = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${StringConstants.loadProvincesError}: $e'),
            backgroundColor: ColorConstants.errorMedium,
          ),
        );
      }
    }
  }
  
  // Load wards by province code
  Future<void> _loadWards(String provinceCode) async {
    setState(() {
      _isLoadingWards = true;
      _selectedWard = null;
      _wards = [];
    });
    
    try {
      final wards = await LocationService.getWardsByProvinceCode(provinceCode);
      setState(() {
        _wards = wards;
        _isLoadingWards = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingWards = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${StringConstants.loadWardsError}: $e'),
            backgroundColor: ColorConstants.errorMedium,
          ),
        );
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.backgroundColor,
      appBar: custom_app_bar_theme.AppBarTheme.createAuthAppBar(
        title: StringConstants.registrationTitle,
        onBackPressed: () => BackNavigationHelper.safeNavigateBack(context),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(SpacingConstants.paddingMedium),
          child: Column(
            children: [
              _buildFullNameField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildGenderField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildBirthYearField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildProvinceField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildWardField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildPhoneField(),
              const SizedBox(height: SpacingConstants.spacingL),
              _buildEmailField(),
              const SizedBox(height: SpacingConstants.spacingXXL),
              _buildButtons(),
            ],
          ),
        ),
      ),
    );
  }
  
  Widget _buildFullNameField() {
    return TextFormField(
      controller: _fullNameController,
      maxLength: NumericConstants.fullNameMaxLength,
      decoration: InputDecoration(
        labelText: StringConstants.fullNameLabel,
        hintText: StringConstants.fullNamePlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return StringConstants.requiredField;
        }
        return null;
      },
    );
  }
  
  Widget _buildGenderField() {
    return DropdownButtonFormField<String>(
      value: _selectedGender,
      decoration: InputDecoration(
        labelText: StringConstants.genderLabel,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
      ),
      items: _genderOptions.map((String gender) {
        return DropdownMenuItem<String>(
          value: gender,
          child: Text(gender),
        );
      }).toList(),
      onChanged: (String? newValue) {
        setState(() {
          _selectedGender = newValue;
        });
      },
      validator: (value) {
        if (value == null || value.isEmpty) {
          return StringConstants.requiredField;
        }
        return null;
      },
    );
  }
  
  Widget _buildBirthYearField() {
    return DropdownButtonFormField<int>(
      value: _selectedBirthYear,
      decoration: InputDecoration(
        labelText: StringConstants.birthYearLabel,
        hintText: StringConstants.birthYearPlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
      ),
      items: _birthYears.map((int year) {
        return DropdownMenuItem<int>(
          value: year,
          child: Text(year.toString()),
        );
      }).toList(),
      onChanged: (int? newValue) {
        setState(() {
          _selectedBirthYear = newValue;
        });
      },
      validator: (value) {
        if (value == null) {
          return StringConstants.requiredField;
        }
        return null;
      },
    );
  }
  
  Widget _buildWardField() {
    return DropdownButtonFormField<Ward>(
      value: _selectedWard,
      decoration: InputDecoration(
        labelText: StringConstants.wardLabel,
        hintText: _selectedProvince == null 
            ? StringConstants.selectProvinceFirst
            : StringConstants.wardPlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
        suffixIcon: _isLoadingWards 
            ? const SizedBox(
                width: SizeConstants.loadingIndicatorMedium,
                height: SizeConstants.loadingIndicatorMedium,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : null,
      ),
      items: _wards.map((Ward ward) {
        return DropdownMenuItem<Ward>(
          value: ward,
          child: Text(ward.name),
        );
      }).toList(),
      onChanged: _selectedProvince == null || _isLoadingWards 
          ? null 
          : (Ward? newValue) {
              setState(() {
                _selectedWard = newValue;
              });
            },
      validator: (value) {
        if (value == null) {
          return StringConstants.requiredField;
        }
        return null;
      },
    );
  }
  
  Widget _buildProvinceField() {
    return DropdownButtonFormField<Province>(
      value: _selectedProvince,
      decoration: InputDecoration(
        labelText: StringConstants.provinceLabel,
        hintText: StringConstants.provincePlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
        suffixIcon: _isLoadingProvinces 
            ? const SizedBox(
                width: SizeConstants.loadingIndicatorMedium,
                height: SizeConstants.loadingIndicatorMedium,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : null,
      ),
      items: _provinces.map((Province province) {
        return DropdownMenuItem<Province>(
          value: province,
          child: Text(province.name),
        );
      }).toList(),
      onChanged: _isLoadingProvinces 
          ? null 
          : (Province? newValue) {
              setState(() {
                _selectedProvince = newValue;
                _selectedWard = null;
                _wards = [];
              });
              if (newValue != null) {
                _loadWards(newValue.provinceCode);
              }
            },
      validator: (value) {
        if (value == null) {
          return StringConstants.requiredField;
        }
        return null;
      },
    );
  }
  
  Widget _buildPhoneField() {
    return TextFormField(
      controller: _phoneController,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(NumericConstants.phoneMaxLength),
      ],
      decoration: InputDecoration(
        labelText: StringConstants.phoneLabel,
        hintText: StringConstants.phonePlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return StringConstants.requiredField;
        }
        if (value.length < NumericConstants.phoneMinLength || 
            value.length > NumericConstants.phoneMaxLength) {
          return StringConstants.invalidPhone;
        }
        return null;
      },
    );
  }
  
  Widget _buildEmailField() {
    return TextFormField(
      controller: _emailController,
      keyboardType: TextInputType.emailAddress,
      maxLength: NumericConstants.emailMaxLength,
      decoration: InputDecoration(
        labelText: StringConstants.emailLabel,
        hintText: StringConstants.emailPlaceholder,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
          borderSide: const BorderSide(color: ColorConstants.focusedBorder),
        ),
        filled: true,
        fillColor: ColorConstants.surfaceColor,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return StringConstants.requiredField;
        }
        if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
          return StringConstants.invalidEmail;
        }
        return null;
      },
    );
  }
  
  Widget _buildButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _handleCancel,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.buttonSecondaryHigh,
              foregroundColor: ColorConstants.buttonTextColor,
              minimumSize: const Size(double.infinity, SizeConstants.buttonHeightMedium),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
              ),
            ),
            child: const Text(StringConstants.cancelButton),
          ),
        ),
        const SizedBox(width: SpacingConstants.spacingL),
        Expanded(
          child: ElevatedButton(
            onPressed: _handleRegister,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorConstants.buttonPrimaryMedium,
              foregroundColor: ColorConstants.buttonTextColor,
              minimumSize: const Size(double.infinity, SizeConstants.buttonHeightMedium),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(SizeConstants.borderRadiusMedium),
              ),
            ),
            child: const Text(StringConstants.registerButton),
          ),
        ),
      ],
    );
  }
  
  void _handleCancel() {
    BackNavigationHelper.safeNavigateBack(context);
  }
  
  void _handleRegister() {
    if (_formKey.currentState?.validate() ?? false) {
      // Process registration data
      final registrationData = {
        'fullName': _fullNameController.text.trim(),
        'gender': _selectedGender,
        'birthYear': _selectedBirthYear,
        'province': _selectedProvince?.name,
        'provinceCode': _selectedProvince?.provinceCode,
        'ward': _selectedWard?.name,
        'wardCode': _selectedWard?.wardCode,
        'phone': _phoneController.text.trim(),
        'email': _emailController.text.trim(),
      };
      
      // Navigate directly to password setup screen (skip phone verification)
<<<<<<< HEAD
      BackNavigationHelper.navigateWithRefresh(
=======
      Navigator.push(
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
        context,
        SetPasswordScreen(
          registrationData: registrationData,
        ),
      );
    }
  }
}