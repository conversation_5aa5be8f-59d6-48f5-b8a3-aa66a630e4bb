import 'package:flutter/material.dart';
import '../models/department.dart';
import '../models/doctor.dart';
import '../services/appointment_service.dart';
import '../services/doctor_filter_service.dart';
import '../widgets/doctor_filter_widget.dart';
import 'book_appointment_screen.dart';

class DoctorFilterScreen extends StatefulWidget {
  final Department department;
<<<<<<< HEAD
  final Function(DoctorFilter) onFilterApplied;

  const DoctorFilterScreen({
    super.key,
    required this.department,
    required this.onFilterApplied,
  });
=======

  const DoctorFilterScreen({
    Key? key,
    required this.department,
  }) : super(key: key);
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697

  @override
  State<DoctorFilterScreen> createState() => _DoctorFilterScreenState();
}

class _DoctorFilterScreenState extends State<DoctorFilterScreen> {
  List<Doctor> allDoctors = [];
  List<Doctor> filteredDoctors = [];
  DoctorFilter currentFilter = const DoctorFilter();
  bool isLoading = true;
  String? error;
  Map<String, dynamic>? filterStats;

  @override
  void initState() {
    super.initState();
    _loadDoctors();
  }

  Future<void> _loadDoctors() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final doctors = await AppointmentService.getDoctorsByDepartment(widget.department.id!);
      
      setState(() {
        allDoctors = doctors;
        filteredDoctors = doctors; // Initially show all doctors
        isLoading = false;
      });

      _updateFilterStats();
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  void _onFilterChanged(DoctorFilter filter) async {
    setState(() {
      currentFilter = filter;
    });

    if (filter.isEmpty) {
      setState(() {
        filteredDoctors = allDoctors;
      });
    } else {
      try {
        final filtered = await DoctorFilterService.filterDoctors(
          doctors: allDoctors,
          filter: filter,
        );
        
        setState(() {
          filteredDoctors = filtered;
        });
      } catch (e) {
        print('❌ Error filtering doctors: $e');
        setState(() {
          filteredDoctors = allDoctors;
        });
      }
    }

    _updateFilterStats();
  }

  void _updateFilterStats() {
    setState(() {
      filterStats = DoctorFilterService.getFilterStats(
        originalDoctors: allDoctors,
        filteredDoctors: filteredDoctors,
        filter: currentFilter,
      );
    });
  }

  void _navigateToBooking(Doctor doctor) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BookAppointmentScreen(
          doctor: doctor,
          department: widget.department,
          initialDate: currentFilter.date,
          initialTimeSlot: currentFilter.timeSlot,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Khoa ${widget.department.name}'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDoctors,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải danh sách bác sĩ...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Có lỗi xảy ra: $error',
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.red),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDoctors,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Filter Widget
        DoctorFilterWidget(
          onFilterChanged: _onFilterChanged,
          initialFilter: currentFilter,
        ),
        
        // Filter Statistics
        if (filterStats != null) _buildFilterStats(),
        
        // Doctor List
        Expanded(
          child: _buildDoctorList(),
        ),
      ],
    );
  }

  Widget _buildFilterStats() {
    if (filterStats == null) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Tìm thấy ${filterStats!['filteredDoctors']} bác sĩ '
              '(trên tổng ${filterStats!['totalDoctors']} bác sĩ)',
              style: TextStyle(
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoctorList() {
    if (filteredDoctors.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_search,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              currentFilter.isEmpty
                  ? 'Khoa này chưa có bác sĩ'
                  : 'Không tìm thấy bác sĩ phù hợp với bộ lọc',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 16,
              ),
            ),
            if (!currentFilter.isEmpty) ...[
              const SizedBox(height: 8),
              TextButton(
                onPressed: () => _onFilterChanged(const DoctorFilter()),
                child: const Text('Xóa bộ lọc'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: filteredDoctors.length,
      itemBuilder: (context, index) {
        final doctor = filteredDoctors[index];
        return _buildDoctorCard(doctor);
      },
    );
  }

  Widget _buildDoctorCard(Doctor doctor) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToBooking(doctor),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 25,
<<<<<<< HEAD
                    backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
=======
                    backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                    child: Text(
                      doctor.fullName.isNotEmpty 
                          ? doctor.fullName[0].toUpperCase()
                          : 'BS',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          doctor.fullName,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          doctor.specialization,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '${doctor.consultationFee.toStringAsFixed(0)}đ',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                      Text(
                        doctor.experience,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 12),
              
              // Available days
              Wrap(
                spacing: 4,
                children: doctor.availableDays.map((day) {
                  final isMatchingFilter = currentFilter.dayOfWeek == day ||
                      (currentFilter.date != null && 
                       day == _getDayOfWeekVietnamese(currentFilter.date!));
                  
                  return Chip(
                    label: Text(
                      day,
                      style: TextStyle(
                        fontSize: 12,
                        color: isMatchingFilter ? Colors.white : null,
                      ),
                    ),
                    backgroundColor: isMatchingFilter 
                        ? Theme.of(context).primaryColor
                        : Colors.grey.shade200,
                    padding: EdgeInsets.zero,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.phone,
                    size: 16,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    doctor.phone,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                  const Spacer(),
                  const Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getDayOfWeekVietnamese(DateTime date) {
    const days = [
      'Thứ 2', 'Thứ 3', 'Thứ 4', 'Thứ 5', 'Thứ 6', 'Thứ 7', 'Chủ nhật'
    ];
    return days[date.weekday - 1];
  }
}
