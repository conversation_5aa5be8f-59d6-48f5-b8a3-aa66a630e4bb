import 'package:flutter_dotenv/flutter_dotenv.dart';

class MongoDBConfig {
  // MongoDB Atlas Data API configuration - Secure Version
  
  /// Load environment variables (call this in main())
  static Future<void> loadEnv() async {
    try {
      await dotenv.load(fileName: ".env");
    } catch (e) {
      print('Warning: Could not load .env file. Using default values.');
    }
  }
  
  // Base URL for MongoDB Data API - loaded from environment
  static String get baseUrl {
    final appId = dotenv.env['MONGODB_APP_ID'] ?? 'YOUR_APP_ID';
    return 'https://data.mongodb-api.com/app/$appId/endpoint/data/v1';
  }
  
  // Data source name (tên cluster) - from environment or default
  static String get dataSource => 
      dotenv.env['MONGODB_DATA_SOURCE'] ?? 'MedixCareDB';
  
  // Database name - from environment or default
  static String get database => 
      dotenv.env['MONGODB_DATABASE'] ?? 'medixcare';
  
  // API Key - loaded securely from environment
  static String get apiKey => 
      dotenv.env['MONGODB_SECRET_KEY'] ?? 'NOT_CONFIGURED';
  
  // Collection names
  static const String usersCollection = 'users';
  static const String sessionsCollection = 'sessions';
  
  /// Validate configuration
  static bool isConfigured() {
    return apiKey != 'NOT_CONFIGURED' && 
           apiKey.isNotEmpty &&
           baseUrl.contains('YOUR_APP_ID') == false;
  }
  
  /// Get configuration status message
  static String getConfigMessage() {
    if (!isConfigured()) {
      return '''
🔐 MongoDB chưa được cấu hình an toàn!

Để cấu hình bảo mật:
1. Tạo file .env trong thư mục root project
2. Thêm các biến environment:
   MONGODB_APP_ID=your-app-id
   MONGODB_SECRET_KEY=your-secret-key
   MONGODB_DATA_SOURCE=MedixCareDB
   MONGODB_DATABASE=medixcare

3. File .env đã được thêm vào .gitignore (an toàn)
4. Gọi MongoDBConfig.loadEnv() trong main()

⚠️ Không bao giờ commit file .env lên git!
      ''';
    }
    return '✅ MongoDB đã được cấu hình an toàn';
  }
  
  /// Debug info (không hiện secret key)
  static Map<String, String> getDebugInfo() {
    return {
      'baseUrl': baseUrl,
      'dataSource': dataSource,
      'database': database,
      'apiKeyConfigured': apiKey != 'NOT_CONFIGURED' ? 'Yes' : 'No',
      'usersCollection': usersCollection,
    };
  }
}
