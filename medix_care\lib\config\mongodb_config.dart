class MongoDBConfig {
  // MongoDB Configuration - Hybrid approach
  // Sử dụng connection string info cho Data API
  
  // Connection String (for reference and parsing)
  static const String connectionString = 'mongodb+srv://thanhhai3009hutech:<EMAIL>/medixcare?retryWrites=true&w=majority&appName=Cluster0';
  
  // Parsed from connection string
  static const String dataSource = 'Cluster0';  // From appName parameter
  static const String database = 'medixcare';   // From path
  
  // For Data API - you still need App Services setup
  // Base URL for MongoDB Data API (cần App ID từ App Services)
  static const String baseUrl = 'https://data.mongodb-api.com/app/YOUR_APP_ID/endpoint/data/v1';
  
  // API Key from App Services (not the database password!)
  static const String apiKey = 'YOUR_SECRET_API_KEY_HERE';
  
  // Collection names
  static const String usersCollection = 'users';
  static const String sessionsCollection = 'sessions';
  
  /// Parse connection info from connection string
  static Map<String, String> get connectionInfo {
    return {
      'host': 'cluster0.6emrmpm.mongodb.net',
      'database': database,
      'username': 'thanhhai3009hutech',
      'cluster': dataSource,
      'fullConnectionString': connectionString,
    };
  }
  
  /// Instructions để thiết lập MongoDB Atlas:
  /// 
  /// ✅ BẠN ĐÃ CÓ: Connection String và Database
  /// ❌ CẦN THÊM: App Services để dùng với Flutter
  /// 
  /// Bước tiếp theo:
  /// 1. Vào MongoDB Atlas (https://cloud.mongodb.com)
  /// 2. Sidebar: App Services > Create App
  /// 3. Chọn cluster: Cluster0 (cluster của bạn)
  /// 4. Đặt tên app: medixcare-api
  /// 5. Enable Data API trong HTTPS Endpoints
  /// 6. Copy App ID từ App Settings
  /// 7. Tạo API Key trong Authentication > API Keys
  /// 8. Cập nhật baseUrl và apiKey ở trên
  
  /// Validate configuration
  static bool isConfigured() {
    return apiKey != 'YOUR_SECRET_API_KEY_HERE' && 
           apiKey.isNotEmpty &&
           baseUrl.contains('YOUR_APP_ID') == false;
  }
  
  /// Get configuration status message
  static String getConfigMessage() {
    final info = connectionInfo;
    
    if (!isConfigured()) {
      return '''
🔄 MongoDB Connection String đã có!

✅ Thông tin hiện có:
- Cluster: ${info['cluster']}
- Database: ${info['database']}
- Username: ${info['username']}
- Host: ${info['host']}

❌ Còn thiếu cho Flutter:
- App Services App ID
- Data API Secret Key

📋 Bước tiếp theo:
1. Vào MongoDB Atlas > App Services
2. Create App với cluster Cluster0
3. Enable Data API
4. Copy App ID và Secret Key
5. Cập nhật baseUrl và apiKey

💡 Hoặc có thể test với mock data trước!
      ''';
    }
    return '✅ MongoDB đã được cấu hình đầy đủ cho Flutter!';
  }
}
