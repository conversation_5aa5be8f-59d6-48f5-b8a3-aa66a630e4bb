import 'package:flutter/material.dart';

class ColorConstants {
  // Primary colors - with variants
  static const Color primaryLow = Color(0xFF64B5F6);     // Light blue
  static const Color primaryMedium = Color(0xFF2196F3);   // Main blue
  static const Color primaryHigh = Color(0xFF1976D2);     // Dark blue
  
  // Background colors
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundMedium = Color(0xFFF5F5F5);
  static const Color backgroundDark = Color(0xFFEEEEEE);
  static const Color surfaceColor = Colors.white;
  
  // Text colors
  static const Color textPrimaryHigh = Color(0xFF212121);    // Very dark
  static const Color textPrimaryMedium = Color(0xFF424242);  // Medium dark
  static const Color textPrimaryLow = Color(0xFF757575);     // Light dark
  static const Color textSecondary = Color(0xFF9E9E9E);     // Gray
  static const Color hintText = Color(0xFFBDBDBD);          // Light gray
  
  // Border colors
  static const Color borderLight = Color(0xFFE0E0E0);       // Light border
  static const Color borderMedium = Color(0xFFBDBDBD);      // Medium border
  static const Color borderHigh = Color(0xFF9E9E9E);        // Dark border
  static const Color focusedBorder = Color(0xFF2196F3);     // Focused state
  
  // Status colors
  static const Color successLow = Color(0xFFC8E6C9);        // Light green
  static const Color successMedium = Color(0xFF4CAF50);     // Green
  static const Color successHigh = Color(0xFF388E3C);       // Dark green
  
  static const Color errorLow = Color(0xFFFFCDD2);          // Light red
  static const Color errorMedium = Color(0xFFE57373);       // Red
  static const Color errorHigh = Color(0xFFD32F2F);         // Dark red
  
  static const Color warningLow = Color(0xFFFFF3C4);        // Light orange
  static const Color warningMedium = Color(0xFFFFB74D);     // Orange
  static const Color warningHigh = Color(0xFFF57C00);       // Dark orange
  
  static const Color infoLow = Color(0xFFE1F5FE);           // Light blue
  static const Color infoMedium = Color(0xFF29B6F6);        // Blue
  static const Color infoHigh = Color(0xFF0277BD);          // Dark blue
  
  // Button colors
  static const Color buttonPrimaryLow = primaryLow;
  static const Color buttonPrimaryMedium = primaryMedium;
  static const Color buttonPrimaryHigh = primaryHigh;
  static const Color buttonTextColor = Colors.white;
  
  static const Color buttonSecondaryLow = Color(0xFFEEEEEE);
  static const Color buttonSecondaryMedium = Color(0xFF9E9E9E);
  static const Color buttonSecondaryHigh = Color(0xFF757575);
  
  // Disabled colors
  static const Color disabledBackground = Color(0xFFE0E0E0);
  static const Color disabledText = Color(0xFF9E9E9E);
  
  // Legacy support - mapping to new constants
  static const Color primaryColor = primaryMedium;
  static const Color primaryLightColor = primaryLow;
  static const Color primaryDarkColor = primaryHigh;
  static const Color backgroundColor = backgroundMedium;
  static const Color textPrimaryColor = textPrimaryHigh;
  static const Color textSecondaryColor = textPrimaryLow;
  static const Color hintTextColor = hintText;
  static const Color borderColor = borderLight;
  static const Color focusedBorderColor = focusedBorder;
  static const Color errorBorderColor = errorMedium;
  static const Color buttonColor = buttonPrimaryMedium;
  static const Color cancelButtonColor = buttonSecondaryHigh;
}
