import 'package:flutter/material.dart';
import '../services/jwt_service.dart';
import '../screens/login_screen.dart';

/// Navigation middleware to handle authentication checks and prevent unexpected logouts
class NavigationMiddleware {
  /// Check authentication before navigation
  static Future<bool> checkAuthBeforeNavigation(BuildContext context) async {
    try {
      final isAuthenticated = await JWTService.isAuthenticated();
      
      if (!isAuthenticated) {
        // Show login dialog instead of immediately logging out
        final shouldLogin = await _showAuthExpiredDialog(context);
        if (shouldLogin) {
          _navigateToLogin(context);
        }
        return false;
      }
      
      return true;
    } catch (e) {
      // Don't block navigation on auth check errors
      return true;
    }
  }

  /// Show authentication expired dialog
  static Future<bool> _showAuthExpiredDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('<PERSON>ên đăng nhập hết hạn'),
        content: const Text(
          '<PERSON>ên đăng nhập của bạn đã hết hạn. Vui lòng đăng nhập lại để tiếp tục.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Để sau'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Đăng nhập'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Navigate to login screen safely
  static void _navigateToLogin(BuildContext context) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
        settings: const RouteSettings(name: '/login'),
      ),
      (route) => route.isFirst, // Keep only the first route (HomeScreen)
    );
  }

  /// Safe navigation with authentication check
  static Future<T?> navigateWithAuthCheck<T>(
    BuildContext context,
    Widget screen, {
    RouteSettings? settings,
    bool requireAuth = true,
  }) async {
    if (requireAuth) {
      final canNavigate = await checkAuthBeforeNavigation(context);
      if (!canNavigate) return null;
    }

    return Navigator.push<T>(
      context,
      MaterialPageRoute(
        builder: (context) => screen,
        settings: settings,
      ),
    );
  }

  /// Safe navigation replacement with authentication check
  static Future<T?> navigateReplacementWithAuthCheck<T>(
    BuildContext context,
    Widget screen, {
    RouteSettings? settings,
    bool requireAuth = true,
  }) async {
    if (requireAuth) {
      final canNavigate = await checkAuthBeforeNavigation(context);
      if (!canNavigate) return null;
    }

    return Navigator.pushReplacement<T, dynamic>(
      context,
      MaterialPageRoute(
        builder: (context) => screen,
        settings: settings,
      ),
    );
  }

  /// Safe back navigation with stack preservation
  static void safeNavigateBack(BuildContext context, {dynamic result}) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context, result);
    } else {
      // If we can't pop, we're at the root - don't exit the app
      // Instead, show a message or navigate to a safe screen
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Đã ở trang chủ'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// Navigate back to a specific route in the stack
  static bool navigateBackToRoute(BuildContext context, String routeName) {
    try {
      Navigator.of(context).popUntil((route) {
        return route.settings.name == routeName || route.isFirst;
      });
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Check if a specific route exists in the navigation stack
  static bool routeExistsInStack(BuildContext context, String routeName) {
    bool exists = false;
    Navigator.of(context).popUntil((route) {
      if (route.settings.name == routeName) {
        exists = true;
      }
      return true; // Don't actually pop, just check
    });
    return exists;
  }

  /// Get current route name
  static String? getCurrentRouteName(BuildContext context) {
    return ModalRoute.of(context)?.settings.name;
  }

  /// Navigate with proper error handling
  static Future<T?> navigateWithErrorHandling<T>(
    BuildContext context,
    Widget screen, {
    RouteSettings? settings,
    bool requireAuth = true,
    String? errorMessage,
  }) async {
    try {
      return await navigateWithAuthCheck<T>(
        context,
        screen,
        settings: settings,
        requireAuth: requireAuth,
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage ?? 'Lỗi điều hướng: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return null;
    }
  }

  /// Clear navigation stack safely
  static void clearStackSafely(BuildContext context, Widget homeScreen) {
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (context) => homeScreen,
        settings: const RouteSettings(name: '/home'),
      ),
      (route) => false,
    );
  }
}

/// Extension for convenient navigation methods
extension NavigationMiddlewareExtension on BuildContext {
  /// Navigate with authentication check
  Future<T?> navigateWithAuth<T>(Widget screen, {RouteSettings? settings}) {
    return NavigationMiddleware.navigateWithAuthCheck<T>(
      this,
      screen,
      settings: settings,
    );
  }

  /// Safe navigate back
  void safeNavigateBack({dynamic result}) {
    NavigationMiddleware.safeNavigateBack(this, result: result);
  }

  /// Navigate back to specific route
  bool navigateBackToRoute(String routeName) {
    return NavigationMiddleware.navigateBackToRoute(this, routeName);
  }

  /// Get current route name
  String? get currentRouteName => NavigationMiddleware.getCurrentRouteName(this);
}
