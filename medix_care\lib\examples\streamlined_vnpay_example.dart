import 'package:flutter/material.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../screens/streamlined_vnpay_screen.dart';

/// Example demonstrating how to use the StreamlinedVNPayScreen
/// 
/// This example shows how to integrate the new streamlined VNPay payment screen
/// into your app's payment flow.
class StreamlinedVNPayExample extends StatelessWidget {
  const StreamlinedVNPayExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Streamlined VNPay Example'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'Streamlined VNPay Payment Screen',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This new payment screen provides:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 12),
            const Text('• Clear payment information display'),
            const Text('• VNPay branding and instructions'),
            const Text('• WebView or external browser support'),
            const Text('• Proper error handling'),
            const Text('• Loading states during processing'),
            const Text('• Smooth animations and modern UI'),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => _showWebViewExample(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Demo with WebView'),
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () => _showExternalBrowserExample(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text('Demo with External Browser'),
            ),
            const SizedBox(height: 32),
            const Text(
              'Integration Instructions:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Text(
                '''1. Import the screen:
import 'screens/streamlined_vnpay_screen.dart';

2. Navigate to the screen:
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => StreamlinedVNPayScreen(
      appointment: appointment,
      doctor: doctor,
      useWebView: true, // or false for external browser
    ),
  ),
);

3. The screen will handle:
   - Payment URL creation
   - Payment processing
   - Result handling
   - Error management''',
                style: TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showWebViewExample(BuildContext context) {
    final appointment = _createSampleAppointment();
    final doctor = _createSampleDoctor();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StreamlinedVNPayScreen(
          appointment: appointment,
          doctor: doctor,
          useWebView: true,
        ),
      ),
    );
  }

  void _showExternalBrowserExample(BuildContext context) {
    final appointment = _createSampleAppointment();
    final doctor = _createSampleDoctor();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => StreamlinedVNPayScreen(
          appointment: appointment,
          doctor: doctor,
          useWebView: false,
        ),
      ),
    );
  }

  Appointment _createSampleAppointment() {
    return Appointment(
      id: 'sample_appointment_123',
      patientId: 'patient_123',
      doctorId: 'doctor_123',
      departmentId: 'dept_123',
      appointmentDate: DateTime.now().add(const Duration(days: 1)),
      appointmentTime: '14:30',
      symptoms: 'Đau đầu, sốt nhẹ',
      notes: 'Cần khám tổng quát',
      consultationFee: 200000.0, // 200,000 VND
      createdAt: DateTime.now(),
    );
  }

  Doctor _createSampleDoctor() {
    return Doctor(
      id: 'doctor_123',
      fullName: 'Nguyễn Văn An',
      title: 'Tiến sĩ',
      specialization: 'Nội khoa',
      departmentId: 'dept_123',
      experience: '10 năm',
      education: 'Đại học Y Hà Nội',
      phone: '**********',
      email: '<EMAIL>',
      description: 'Bác sĩ chuyên khoa Nội với 10 năm kinh nghiệm',
      consultationFee: 200000.0,
      availableDays: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      createdAt: DateTime.now(),
    );
  }
}

/// Usage in your app:
/// 
/// ```dart
/// // In your payment method selection screen:
/// ElevatedButton(
///   onPressed: () {
///     Navigator.push(
///       context,
///       MaterialPageRoute(
///         builder: (context) => StreamlinedVNPayScreen(
///           appointment: appointment,
///           doctor: doctor,
///           useWebView: true, // Choose WebView or external browser
///         ),
///       ),
///     );
///   },
///   child: Text('Thanh toán VNPay'),
/// )
/// ```
/// 
/// Features:
/// - Modern, streamlined UI with smooth animations
/// - Clear payment information display
/// - VNPay branding and security indicators
/// - Step-by-step payment instructions
/// - Support for both WebView and external browser
/// - Comprehensive error handling with retry options
/// - Loading states and progress indicators
/// - Automatic payment result processing
/// - Integration with existing PaymentResultScreen
/// 
/// The screen automatically:
/// 1. Creates VNPay payment URL using VNPayService.createPaymentUrl()
/// 2. Displays payment information clearly
/// 3. Shows VNPay branding and instructions
/// 4. Handles payment flow (WebView or external browser)
/// 5. Processes payment results
/// 6. Redirects to PaymentResultScreen with appropriate status
/// 7. Handles errors with user-friendly messages and retry options
