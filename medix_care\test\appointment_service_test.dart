import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/services/appointment_service.dart';
import 'package:medix_care/services/database_seeder.dart';
import 'package:medix_care/models/appointment.dart';

void main() {
  group('Appointment Service Tests', () {
    setUpAll(() async {
      // Initialize the service before running tests
      final connected = await AppointmentService.initialize();
      expect(connected, isTrue, reason: 'Should connect to MongoDB successfully');
    });

    tearDownAll(() async {
      // Close connection after tests
      await AppointmentService.close();
    });

    test('should connect to MongoDB successfully', () async {
      final connected = await AppointmentService.testConnection();
      expect(connected, isTrue);
      print('✅ MongoDB connection test passed');
    });

    test('should seed initial data successfully', () async {
      try {
        await DatabaseSeeder.seedInitialData();
        print('✅ Database seeding completed');
        
        // Verify departments were created
        final departments = await AppointmentService.getAllDepartments();
        expect(departments, isNotEmpty, reason: 'Should have departments after seeding');
        print('📋 Found ${departments.length} departments');
        
        for (final dept in departments) {
          print('  - ${dept.name}: ${dept.description}');
        }
        
      } catch (e) {
        print('❌ Seeding failed: $e');
        // Don't fail the test if data already exists
        if (!e.toString().contains('đã tồn tại')) {
          fail('Failed to seed data: $e');
        }
      }
    });

    test('should fetch all departments', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        print('📋 Departments count: ${departments.length}');
        
        expect(departments, isNotEmpty);
        
        for (final dept in departments) {
          print('  - ${dept.name}');
          expect(dept.name, isNotEmpty);
          expect(dept.description, isNotEmpty);
        }
      } catch (e) {
        print('❌ Error fetching departments: $e');
        fail('Failed to fetch departments: $e');
      }
    });

    test('should fetch doctors by department', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        if (departments.isNotEmpty) {
          final firstDept = departments.first;
          final doctors = await AppointmentService.getDoctorsByDepartment(firstDept.id!);
          
          print('👨‍⚕️ Doctors in ${firstDept.name}: ${doctors.length}');
          
          for (final doctor in doctors) {
            print('  - ${doctor.title} ${doctor.fullName} - ${doctor.specialization}');
            print('    📧 ${doctor.email}');
            print('    💰 Phí khám: ${doctor.consultationFee.toStringAsFixed(0)} VNĐ');
            print('    📅 Ngày khám: ${doctor.availableDays.join(', ')}');
            
            expect(doctor.fullName, isNotEmpty);
            expect(doctor.email, isNotEmpty);
            expect(doctor.consultationFee, greaterThan(0));
          }
        }
      } catch (e) {
        print('❌ Error fetching doctors: $e');
        fail('Failed to fetch doctors: $e');
      }
    });

    test('should fetch doctor schedules', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        if (departments.isNotEmpty) {
          final doctors = await AppointmentService.getDoctorsByDepartment(departments.first.id!);
          
          if (doctors.isNotEmpty) {
            final firstDoctor = doctors.first;
            final schedules = await AppointmentService.getDoctorSchedules(firstDoctor.id!);
            
            print('📅 Schedules for ${firstDoctor.fullName}: ${schedules.length}');
            
            for (final schedule in schedules) {
              print('  - ${schedule.dayOfWeek}: ${schedule.startTime} - ${schedule.endTime}');
              print('    Nghỉ trưa: ${schedule.breakStartTime} - ${schedule.breakEndTime}');
              print('    Slot: ${schedule.slotDuration} phút/slot');
              
              // Test time slot generation
              final timeSlots = schedule.generateTimeSlots();
              print('    Available slots: ${timeSlots.length} slots');
              print('    First few slots: ${timeSlots.take(5).join(', ')}');
              
              expect(schedule.dayOfWeek, isNotEmpty);
              expect(timeSlots, isNotEmpty);
            }
          }
        }
      } catch (e) {
        print('❌ Error fetching schedules: $e');
        fail('Failed to fetch schedules: $e');
      }
    });

    test('should create a test appointment', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        if (departments.isNotEmpty) {
          final doctors = await AppointmentService.getDoctorsByDepartment(departments.first.id!);
          
          if (doctors.isNotEmpty) {
            final doctor = doctors.first;
            
            // Create a test appointment for tomorrow
            final tomorrow = DateTime.now().add(const Duration(days: 1));
            
            final appointment = await AppointmentService.createAppointment(
              patientId: 'test_patient_id', // This would be a real user ID in practice
              doctorId: doctor.id!,
              departmentId: departments.first.id!,
              appointmentDate: tomorrow,
              appointmentTime: '09:00',
              symptoms: 'Đau đầu, chóng mặt',
              notes: 'Triệu chứng xuất hiện từ 3 ngày nay',
              consultationFee: doctor.consultationFee,
            );
            
            if (appointment != null) {
              print('📅 Created test appointment:');
              print('  - ID: ${appointment.id}');
              print('  - Doctor: ${doctor.fullName}');
              print('  - Date: ${appointment.formattedDateTime}');
              print('  - Status: ${appointment.statusVN}');
              print('  - Symptoms: ${appointment.symptoms}');
              
              expect(appointment.id, isNotNull);
              expect(appointment.status, equals(AppointmentStatus.pending));
              expect(appointment.doctorId, equals(doctor.id));
              
              // Test status update
              final updated = await AppointmentService.updateAppointmentStatus(
                appointment.id!,
                AppointmentStatus.confirmed
              );
              expect(updated, isTrue);
              print('  ✅ Successfully updated appointment status to confirmed');
            }
          }
        }
      } catch (e) {
        print('❌ Error creating appointment: $e');
        // Don't fail if it's just a duplicate slot error
        if (!e.toString().contains('đã được đặt')) {
          fail('Failed to create appointment: $e');
        }
      }
    });
  });
}
