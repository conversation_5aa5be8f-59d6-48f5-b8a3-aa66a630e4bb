import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../services/vnpay_service.dart';
import '../constants/app_constants.dart';
import 'payment_result_screen.dart';

class PaymentScreen extends StatefulWidget {
  final Appointment appointment;
  final Doctor doctor;
  final bool useWebView;

  const PaymentScreen({
    super.key,
    required this.appointment,
    required this.doctor,
    this.useWebView = true,
  });

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  bool _isLoading = true;
  String? _errorMessage;
  String? _paymentUrl;
  late WebViewController _webViewController;
  late final bool _useWebView;

  @override
  void initState() {
    super.initState();
    _useWebView = widget.useWebView;
    _initializePayment();
  }

  Future<void> _initializePayment() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Lấy địa chỉ IP của thiết bị (trong môi trường thực tế)
      final ipAddress = await _getIpAddress();
      
      // Tạo URL thanh toán VNPay
      final paymentUrl = await VNPayService.createPaymentUrl(
        widget.appointment,
        ipAddress,
      );
      
      setState(() {
        _paymentUrl = paymentUrl;
        _isLoading = false;
      });
      
      if (_useWebView) {
        // Khởi tạo WebView controller nếu sử dụng WebView
        _webViewController = WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(Colors.white)
          ..setNavigationDelegate(
            NavigationDelegate(
              onNavigationRequest: (NavigationRequest request) {
                // Kiểm tra nếu URL là return URL từ VNPay
                if (request.url.contains('medixcare.com/payment/result')) {
                  _handlePaymentResult(request.url);
                  return NavigationDecision.prevent;
                }
                return NavigationDecision.navigate;
              },
              onPageStarted: (String url) {
                setState(() {
                  _isLoading = true;
                });
              },
              onPageFinished: (String url) {
                setState(() {
                  _isLoading = false;
                });
              },
              onWebResourceError: (WebResourceError error) {
                setState(() {
                  _errorMessage = 'Lỗi: ${error.description}';
                  _isLoading = false;
                });
              },
            ),
          )
          ..loadRequest(Uri.parse(_paymentUrl!));
      } else {
        // Mở URL thanh toán trong trình duyệt ngoài
        _openExternalBrowser();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Lỗi khởi tạo thanh toán: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<void> _openExternalBrowser() async {
    if (_paymentUrl == null) return;
    
    final url = Uri.parse(_paymentUrl!);
    
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      setState(() {
        _errorMessage = 'Không thể mở URL thanh toán';
      });
    }
  }
  
  Future<String> _getIpAddress() async {
    try {
      // Trong môi trường thực, nên gọi API để lấy địa chỉ IP thực của thiết bị
      // Ở đây dùng địa chỉ IP giả định
      return '127.0.0.1';
    } catch (e) {
      return '127.0.0.1'; // Default fallback IP
    }
  }
  
  void _handlePaymentResult(String url) {
    // Parse query parameters từ URL
    final uri = Uri.parse(url);
    final params = uri.queryParameters;
    
    // Kiểm tra kết quả thanh toán từ VNPay
    final responseCode = params['vnp_ResponseCode'];
    final status = responseCode == '00'; // 00 = thành công
    
    // Chuyển sang màn hình kết quả thanh toán
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => PaymentResultScreen(
          appointment: widget.appointment,
          doctor: widget.doctor,
          status: status,
          responseCode: responseCode,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thanh toán VNPay'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Đang khởi tạo thanh toán...'),
                ],
              ),
            )
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        color: Colors.red,
                        size: 64,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _initializePayment,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppConstants.primaryBlue,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Thử lại'),
                      ),
                    ],
                  ),
                )
              : _useWebView
                  ? WebViewWidget(controller: _webViewController)
                  : Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'Thanh toán VNPay',
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Nhấn nút bên dưới để mở trang thanh toán VNPay',
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          ElevatedButton(
                            onPressed: _openExternalBrowser,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppConstants.primaryBlue,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                            ),
                            child: const Text('Mở trang thanh toán'),
                          ),
                        ],
                      ),
                    ),
    );
  }
}
