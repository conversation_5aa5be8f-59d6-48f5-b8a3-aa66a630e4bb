import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;
import '../constants/app_constants.dart';

class EmailService {
  // EmailJS configuration
  static const String _serviceId = 'medix_care_service';
  static const String _verificationTemplateId = 'template_lhu7vp9'; // Template OTP cho mã xác thực
  static const String _passwordResetTemplateId = 'template_hz9b4j9'; // Template reset password với link
  static const String _publicKey = 'BT0rlUZ_2Q4ww5H-2'; // Public key
  
  // EmailJS API endpoints
  static const String _smtpService = 'https://api.emailjs.com/api/v1.0/email/send';

  /// Generate random verification code
  static String generateVerificationCode() {
    final random = Random();
    return (100000 + random.nextInt(900000)).toString(); // 6-digit code
  }

  /// Send verification email using EmailJS
  static Future<bool> sendVerificationEmail({
    required String toEmail,
    required String userName,
    required String verificationCode,
  }) async {
    try {
      print('📧 Sending verification email to: $toEmail');
      print('📧 Using template: $_verificationTemplateId');
      print('📧 Using service: $_serviceId');
      print('📧 Using public key: $_publicKey');
      print('📧 Verification code: $verificationCode');
      
      // Template parameters for EmailJS
      Map<String, dynamic> templateParams = {
        'email': toEmail,              // Dynamic: recipient's email
        'passcode': verificationCode,  // Dynamic: OTP code
        'time': DateTime.now().add(const Duration(minutes: 15)).toString().substring(11, 16), // Dynamic: expiration time (HH:MM format)
        // Static placeholders are configured in EmailJS template
      };

      print('📧 Sending with template params: $templateParams');

      // EmailJS payload for HTTP API
      final emailData = {
        'service_id': _serviceId,
        'template_id': _verificationTemplateId,
        'user_id': _publicKey,
        'template_params': templateParams,
      };

      print('📧 Sending HTTP payload: ${json.encode(emailData)}');

      // Send email using HTTP API
      final response = await http.post(
        Uri.parse(_smtpService),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(emailData),
      );

      print('📧 Email service response: ${response.statusCode}');
      print('📧 Email service body: ${response.body}');

      if (response.statusCode == 200) {
        print('✅ Verification email sent successfully via HTTP API');
        return true;
      } else {
        print('❌ Failed to send email: ${response.statusCode}');
        print('❌ Response body: ${response.body}');
        // Fallback: Simulate email sent (for development)
        return await _simulateEmailSend(toEmail, verificationCode);
      }
    } catch (error) {
      print('❌ Error sending verification email: $error');
      // Fallback: Simulate email sent (for development)
      return await _simulateEmailSend(toEmail, verificationCode);
    }
  }

  /// Simulate email sending for development/testing
  static Future<bool> _simulateEmailSend(String email, String code) async {
    print('🔄 Simulating OTP email send...');
    await Future.delayed(const Duration(seconds: 2)); // Simulate network delay
    
    print('''
📧 === OTP EMAIL SIMULATION ===
To: $email
Template: One-Time Password (OTP)
Passcode: $code
Time: ${DateTime.now().add(const Duration(minutes: 15)).toString().substring(11, 16)}
Subject: Mã xác thực ${AppConstants.appName}
============================
    ''');
    
    return true;
  }

  /// Send password reset email
  static Future<bool> sendPasswordResetEmail({
    required String toEmail,
    required String userName,
    required String resetCode,
  }) async {
    try {
      print('📧 Sending password reset email to: $toEmail');
      
      // Template parameters for EmailJS
      Map<String, dynamic> templateParams = {
        'email': toEmail,       // Dynamic: recipient's email
        'passcode': resetCode,  // Dynamic: reset OTP code 
        'time': DateTime.now().add(const Duration(minutes: 15)).toString().substring(11, 16), // Dynamic: expiration time (HH:MM format)
        // Static placeholders are configured in EmailJS template
      };

      // EmailJS payload for HTTP API
      final emailData = {
        'service_id': _serviceId,
        'template_id': _passwordResetTemplateId,
        'user_id': _publicKey,
        'template_params': templateParams,
      };

      print('📧 Sending password reset HTTP payload: ${json.encode(emailData)}');

      // Send email using HTTP API
      final response = await http.post(
        Uri.parse(_smtpService),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: json.encode(emailData),
      );

      print('📧 Password reset response: ${response.statusCode}');
      print('📧 Password reset body: ${response.body}');

      if (response.statusCode == 200) {
        print('✅ Password reset email sent successfully via HTTP API');
        return true;
      } else {
        print('❌ Failed to send password reset email: ${response.statusCode}');
        print('❌ Response body: ${response.body}');
        return await _simulatePasswordResetEmail(toEmail, resetCode);
      }
    } catch (error) {
      print('❌ Error sending password reset email: $error');
      return await _simulatePasswordResetEmail(toEmail, resetCode);
    }
  }

  /// Simulate password reset email
  static Future<bool> _simulatePasswordResetEmail(String email, String code) async {
    print('🔄 Simulating password reset email...');
    await Future.delayed(const Duration(seconds: 2));
    
    print('''
📧 === PASSWORD RESET EMAIL SIMULATION ===
To: $email
Template: Password Reset with OTP
Passcode: $code
Time: ${DateTime.now().add(const Duration(minutes: 15)).toString().substring(11, 16)}
Subject: Đặt lại mật khẩu ${AppConstants.appName}
============================================
    ''');
    
    return true;
  }

  /// Get service status
  static Map<String, dynamic> getServiceStatus() {
    return {
      'service': 'EmailJS',
      'method': 'HTTP API Direct',
      'endpoint': _smtpService,
      'status': 'Active',
      'fallback': 'Console simulation',
      'templates': {
        'verification': _verificationTemplateId,
        'password_reset': _passwordResetTemplateId,
      },
      'features': [
        'Email verification',
        'Password reset',
        'Console fallback for development'
      ],
    };
  }

  /// Test function to send verification email
  static Future<bool> testVerificationEmail() async {
    print('🧪 Testing verification email...');
    return await sendVerificationEmail(
      toEmail: '<EMAIL>',
      userName: 'Test User',
      verificationCode: '123456',
    );
  }

  /// Debug function to validate EmailJS configuration
  static Future<void> debugEmailJSConfig() async {
    print('🔧 === EmailJS HTTP API Configuration Debug ===');
    print('Service ID: $_serviceId');
    print('Verification Template ID: $_verificationTemplateId'); 
    print('Password Reset Template ID: $_passwordResetTemplateId');
    print('Public Key: $_publicKey');
    print('API Endpoint: $_smtpService');
    print('Method: HTTP API Direct (No Private Key Required)');
    print('=================================================');
    
    // Test API connectivity
    try {
      print('🌐 Testing EmailJS API connectivity...');
      final testResponse = await http.get(Uri.parse('https://api.emailjs.com'));
      print('✅ EmailJS API is reachable: ${testResponse.statusCode}');
    } catch (e) {
      print('❌ EmailJS API connectivity error: $e');
    }
  }
}
