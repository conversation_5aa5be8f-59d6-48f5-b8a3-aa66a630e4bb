import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'mongodb_direct_service.dart';
import '../models/user.dart' as UserModel;
import 'jwt_service.dart';

class FirebaseAuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Sign in with Google
  Future<Map<String, dynamic>> signInWithGoogle() async {
    try {
      // Sign out from any previous sessions
      await _googleSignIn.signOut();
      await _auth.signOut();
      
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        return {
          'success': false,
          'message': 'Google sign in was cancelled'
        };
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _auth.signInWithCredential(credential);
      final User? firebaseUser = userCredential.user;

      if (firebaseUser != null) {
        print('Firebase user info:');
        print('UID: ${firebaseUser.uid}');
        print('Email: ${firebaseUser.email}');
        print('Name: ${firebaseUser.displayName}');
        print('Photo: ${firebaseUser.photoURL}');
        
        // Check if user exists in MongoDB, if not create account
        final mongoResult = await _checkOrCreateUserInMongoDB({
          'email': firebaseUser.email ?? '',
          'name': firebaseUser.displayName ?? 'Google User',
          'phone': '', // Google sign in doesn't provide phone
          'avatar': firebaseUser.photoURL ?? '',
          'googleId': firebaseUser.uid,
        });

        if (mongoResult['success']) {
          return {
            'success': true,
            'message': 'Google sign in successful',
            'user': {
              'email': firebaseUser.email,
              'name': firebaseUser.displayName,
              'avatar': firebaseUser.photoURL,
              'uid': firebaseUser.uid,
            },
            'token': mongoResult['token'],
          };
        } else {
          return {
            'success': false,
            'message': 'Failed to create user in database: ${mongoResult['message']}'
          };
        }
      } else {
        return {
          'success': false,
          'message': 'Failed to get Firebase user information'
        };
      }
    } on FirebaseAuthException catch (e) {
      print('Firebase Auth Error: ${e.code} - ${e.message}');
      return {
        'success': false,
        'message': 'Firebase error: ${e.message}'
      };
    } on Exception catch (e) {
      print('Google Sign In Error: $e');
      return {
        'success': false,
        'message': 'Google sign in failed: ${e.toString()}'
      };
    } catch (e) {
      print('Unexpected Error: $e');
      return {
        'success': false,
        'message': 'Unexpected error: ${e.toString()}'
      };
    }
  }

  // Check or create user in MongoDB
  Future<Map<String, dynamic>> _checkOrCreateUserInMongoDB(Map<String, dynamic> userData) async {
    try {
      print('Checking user in MongoDB with email: ${userData['email']}');
      
      // Initialize MongoDB if not already initialized
      await MongoDBDirectService.initialize();
      
      // Check if user exists
      final existingUser = await MongoDBDirectService.getUserByEmail(userData['email']);
      
      if (existingUser != null) {
        print('User exists in MongoDB: ${existingUser.toJson()}');
        
        // Update last login time
        try {
          if (existingUser.id != null) {
            await _updateUserLastLogin(existingUser.id!);
          }
        } catch (updateError) {
          print('Error updating last login: $updateError');
          // Continue anyway
        }
        
        // Get user data and generate token
        Map<String, dynamic> userJson = existingUser.toJson();
        print('Generating JWT token for existing user: $userJson');
        
        // Create proper User object and save tokens
        final userModel = UserModel.User.fromJson(userJson);
        final accessToken = JWTService.generateAccessToken(userModel);
        final refreshToken = JWTService.generateRefreshToken(userModel); 
        
        // Save tokens to SharedPreferences
        await JWTService.saveTokens(accessToken, refreshToken, userModel);
        
        return {
          'success': true,
          'message': 'User exists',
          'token': accessToken,
          'user': userJson
        };
      } else {
        // User doesn't exist, create new user
        print('User does not exist, creating new user');
        return await _createUserInMongoDB(userData);
      }
    } catch (e) {
      print('Error in _checkOrCreateUserInMongoDB: $e');
      return {
        'success': false,
        'message': 'Database error: ${e.toString()}'
      };
    }
  }

  // Update user last login time
  Future<void> _updateUserLastLogin(String userId) async {
    try {
      await MongoDBDirectService.updateUser(userId, {
        'lastLoginAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      print('Error updating last login: $e');
    }
  }

  // Create new user in MongoDB
  Future<Map<String, dynamic>> _createUserInMongoDB(Map<String, dynamic> userData) async {
    try {
      print('Creating user in MongoDB: $userData');

      final newUser = await MongoDBDirectService.createUser(
        fullName: userData['name'] ?? 'Google User',
        gender: 'Khác', // Default gender
        birthYear: 1990, // Default birth year
        province: 'Chưa xác định', // Default province
        provinceCode: '00', // Default province code
        ward: 'Chưa xác định', // Default ward
        wardCode: '00', // Default ward code
        phone: userData['phone'] ?? '',
        email: userData['email'] ?? '',
        password: 'google_oauth', // Placeholder password for Google users
        isPhoneVerified: false,
      );

      if (newUser != null) {
        print('Created user document: ${newUser.toJson()}');
        
        // Update the user with Google-specific fields
        try {
          if (newUser.id != null) {
            print('Updating user with Google info...');
            final updateResult = await MongoDBDirectService.updateUser(newUser.id!, {
              'avatar': userData['avatar'] ?? '',
              'googleId': userData['googleId'],
              'emailVerified': true,
              'lastLoginAt': DateTime.now().toIso8601String(),
            });
            print('Update result: $updateResult');
          }
        } catch (updateError) {
          print('Error updating user with Google info: $updateError');
          // Continue anyway since user was created successfully
        }
        
        // Get updated user data
        Map<String, dynamic> userJson = newUser.toJson();
        userJson['avatar'] = userData['avatar'] ?? '';
        userJson['googleId'] = userData['googleId'];
        userJson['emailVerified'] = true;
        
        print('Generating JWT token for user: $userJson');
        
        // Create proper User object and save tokens
        final userModel = UserModel.User.fromJson(userJson);
        final accessToken = JWTService.generateAccessToken(userModel);
        final refreshToken = JWTService.generateRefreshToken(userModel);
        
        // Save tokens to SharedPreferences
        await JWTService.saveTokens(accessToken, refreshToken, userModel);
        
        return {
          'success': true,
          'message': 'User created successfully',
          'token': accessToken,
          'user': userJson
        };
      } else {
        return {
          'success': false,
          'message': 'Failed to create user in MongoDB'
        };
      }
    } catch (e) {
      print('Error creating user in MongoDB: $e');
      return {
        'success': false,
        'message': 'Failed to create user: ${e.toString()}'
      };
    }
  }

  // Sign out
  Future<void> signOut() async {
    await _auth.signOut();
    await _googleSignIn.signOut();
    // Also clear JWT tokens
    await JWTService.clearTokens();
  }

  // Check if user is signed in
  bool isSignedIn() {
    return _auth.currentUser != null;
  }

  // Test MongoDB connection
  Future<bool> testConnection() async {
    try {
      return await MongoDBDirectService.testConnection();
    } catch (e) {
      print('Error testing connection: $e');
      return false;
    }
  }

  // Delete user by email (for testing purposes)
  Future<bool> deleteUserByEmail(String email) async {
    try {
      await MongoDBDirectService.initialize();
      final user = await MongoDBDirectService.getUserByEmail(email);
      if (user != null && user.id != null) {
        // Note: MongoDBDirectService doesn't have delete method, 
        // so we'll just return true for now
        print('Would delete user: ${user.id}');
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting user: $e');
      return false;
    }
  }
}
