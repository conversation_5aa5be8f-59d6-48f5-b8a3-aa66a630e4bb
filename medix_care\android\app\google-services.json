{"project_info": {"project_number": "916167099002", "firebase_url": "https://flutter-database-38a16-default-rtdb.asia-southeast1.firebasedatabase.app", "project_id": "flutter-database-38a16", "storage_bucket": "flutter-database-38a16.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:916167099002:android:ccd9d675785c8baf16b1c3", "android_client_info": {"package_name": "com.example.baitap5_2"}}, "oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBk_iM54WQXdaQHjtQ97Az-bFwHIlrhR-w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}, {"client_id": "916167099002-4qk8jv6j3ehollivpehfe7utenkli7t0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.baitap6"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:916167099002:android:59e626b60426c7e516b1c3", "android_client_info": {"package_name": "com.example.baitap6"}}, "oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBk_iM54WQXdaQHjtQ97Az-bFwHIlrhR-w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}, {"client_id": "916167099002-4qk8jv6j3ehollivpehfe7utenkli7t0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.baitap6"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:916167099002:android:35c96f5eccc6630116b1c3", "android_client_info": {"package_name": "com.example.medix_care"}}, "oauth_client": [{"client_id": "916167099002-v2bpcf0fidlj1bmsltqlhb8vhcu3ncc9.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.example.medix_care", "certificate_hash": "8a0b180bd82b4de8b6f6a622e6683ba1973792e3"}}, {"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBk_iM54WQXdaQHjtQ97Az-bFwHIlrhR-w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}, {"client_id": "916167099002-4qk8jv6j3ehollivpehfe7utenkli7t0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.baitap6"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:916167099002:android:3d37343d3349154516b1c3", "android_client_info": {"package_name": "thanhhai.flutter"}}, "oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBk_iM54WQXdaQHjtQ97Az-bFwHIlrhR-w"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "916167099002-u5v6gls5f509i453og73sq60mvmiujrf.apps.googleusercontent.com", "client_type": 3}, {"client_id": "916167099002-4qk8jv6j3ehollivpehfe7utenkli7t0.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.example.baitap6"}}]}}}], "configuration_version": "1"}