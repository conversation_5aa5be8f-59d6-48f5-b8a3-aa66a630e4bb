class ValidationHelper {
  /// Validate Vietnamese phone number
  static bool isValidPhoneNumber(String phone) {
    // Vietnamese phone numbers: 10-11 digits
    return RegExp(r'^[0-9]{10,11}$').hasMatch(phone);
  }

  /// Validate email
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate full name (Vietnamese)
  static bool isValidFullName(String name) {
    // At least 2 characters, allow Vietnamese characters
    return name.trim().length >= 2 && 
           RegExp(r'^[a-zA-ZÀ-ỹ\s]+$').hasMatch(name.trim());
  }

  /// Get phone validation error message
  static String? getPhoneValidationError(String phone) {
    if (phone.isEmpty) {
      return 'Vui lòng nhập số điện thoại';
    }
    if (!isValidPhoneNumber(phone)) {
      return 'Số điện thoạ<PERSON> không hợ<PERSON> lệ (10-11 số)';
    }
    return null;
  }

  /// Get email validation error message
  static String? getEmailValidationError(String email) {
    if (email.isEmpty) {
      return 'Vui lòng nhập email';
    }
    if (!isValidEmail(email)) {
      return 'Email không hợp lệ';
    }
    return null;
  }

  /// Get full name validation error message
  static String? getFullNameValidationError(String name) {
    if (name.trim().isEmpty) {
      return 'Vui lòng nhập họ và tên';
    }
    if (name.trim().length < 2) {
      return 'Họ và tên phải có ít nhất 2 ký tự';
    }
    if (!isValidFullName(name)) {
      return 'Họ và tên chỉ được chứa chữ cái và khoảng trắng';
    }
    return null;
  }

  /// Generate birth years for dropdown
  static List<int> getBirthYears() {
    final currentYear = DateTime.now().year;
    return List.generate(100, (index) => currentYear - index);
  }

  /// Get gender options
  static List<String> getGenderOptions() {
    return ['Nam', 'Nữ', 'Khác'];
  }

  /// Validate verification code
  static bool isValidVerificationCode(String code) {
    // 6-digit verification code
    return RegExp(r'^[0-9]{6}$').hasMatch(code);
  }

  /// Get verification code validation error message
  static String? getVerificationCodeValidationError(String code) {
    if (code.isEmpty) {
      return 'Vui lòng nhập mã xác thực';
    }
    if (!isValidVerificationCode(code)) {
      return 'Mã xác thực phải có 6 chữ số';
    }
    return null;
  }
}
