import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import 'navigation_middleware.dart';

/// Helper class for managing consistent back navigation throughout the app
class BackNavigationHelper {
  /// Creates a consistent AppBar with proper back navigation
  static AppBar createAppBar({
    required String title,
    List<Widget>? actions,
    bool automaticallyImplyLeading = true,
    VoidCallback? onBackPressed,
    bool centerTitle = false,
  }) {
    return AppBar(
      title: Text(title),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: automaticallyImplyLeading && onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: onBackPressed,
            )
          : null,
      actions: actions,
    );
  }

  /// Safe navigation back with context check
  static void safeNavigateBack(BuildContext context, {dynamic result}) {
    NavigationMiddleware.safeNavigateBack(context, result: result);
  }

  /// Navigate back with data refresh callback
  static void navigateBackWithRefresh(
    BuildContext context, {
    VoidCallback? onRefresh,
    dynamic result,
  }) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context, result);
      if (onRefresh != null) {
        // Schedule refresh for next frame to ensure navigation is complete
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onRefresh();
        });
      }
    }
  }

  /// Navigate to screen and refresh previous screen when returning
  static Future<T?> navigateWithRefresh<T>(
    BuildContext context,
    Widget screen, {
    VoidCallback? onReturn,
  }) async {
    final result = await Navigator.push<T>(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
    
    if (onReturn != null) {
      onReturn();
    }
    
    return result;
  }

  /// Replace current screen with new screen
  static Future<T?> navigateReplacement<T>(
    BuildContext context,
    Widget screen,
  ) {
    return Navigator.pushReplacement<T, dynamic>(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  /// Navigate and clear all previous routes
  static Future<T?> navigateAndClearAll<T>(
    BuildContext context,
    Widget screen,
  ) {
    return Navigator.pushAndRemoveUntil<T>(
      context,
      MaterialPageRoute(builder: (context) => screen),
      (route) => false,
    );
  }

  /// Show confirmation dialog before back navigation
  static Future<bool> showBackConfirmationDialog(
    BuildContext context, {
    String title = 'Xác nhận',
    String message = 'Bạn có chắc chắn muốn thoát? Dữ liệu chưa lưu sẽ bị mất.',
    String confirmText = 'Thoát',
    String cancelText = 'Hủy',
  }) async {
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(cancelText),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Create WillPopScope wrapper for forms with unsaved changes
  static Widget createFormWillPopScope({
    required Widget child,
    required bool hasUnsavedChanges,
    String? customMessage,
  }) {
    return WillPopScope(
      onWillPop: () async {
        if (!hasUnsavedChanges) return true;
        
        final context = child.key is GlobalKey 
            ? (child.key as GlobalKey).currentContext 
            : null;
        
        if (context == null) return true;
        
        return await showBackConfirmationDialog(
          context,
          message: customMessage ?? 
              'Bạn có thay đổi chưa được lưu. Bạn có chắc chắn muốn thoát?',
        );
      },
      child: child,
    );
  }

  /// Create WillPopScope wrapper for payment screens
  static Widget createPaymentWillPopScope({
    required Widget child,
    required bool isPaymentInProgress,
  }) {
    return WillPopScope(
      onWillPop: () async {
        if (!isPaymentInProgress) return true;
        
        final context = child.key is GlobalKey 
            ? (child.key as GlobalKey).currentContext 
            : null;
        
        if (context == null) return true;
        
        return await showBackConfirmationDialog(
          context,
          title: 'Cảnh báo',
          message: 'Quá trình thanh toán đang diễn ra. Thoát ngay bây giờ có thể làm mất giao dịch. Bạn có chắc chắn muốn thoát?',
          confirmText: 'Vẫn thoát',
        );
      },
      child: child,
    );
  }

  /// Handle back navigation for specific screen types
  static void handleScreenBack(
    BuildContext context,
    ScreenType screenType, {
    VoidCallback? onRefresh,
    bool hasUnsavedChanges = false,
    bool isPaymentInProgress = false,
  }) async {
    switch (screenType) {
      case ScreenType.form:
        if (hasUnsavedChanges) {
          final shouldExit = await showBackConfirmationDialog(context);
          if (shouldExit) {
            safeNavigateBack(context);
          }
        } else {
          safeNavigateBack(context);
        }
        break;
        
      case ScreenType.payment:
        if (isPaymentInProgress) {
          final shouldExit = await showBackConfirmationDialog(
            context,
            title: 'Cảnh báo',
            message: 'Quá trình thanh toán đang diễn ra. Thoát ngay bây giờ có thể làm mất giao dịch.',
          );
          if (shouldExit) {
            safeNavigateBack(context);
          }
        } else {
          navigateBackWithRefresh(context, onRefresh: onRefresh);
        }
        break;
        
      case ScreenType.normal:
      default:
        navigateBackWithRefresh(context, onRefresh: onRefresh);
        break;
    }
  }
}

/// Enum for different screen types that need special back handling
enum ScreenType {
  normal,
  form,
  payment,
}

/// Extension for BuildContext to add convenient navigation methods
extension NavigationExtension on BuildContext {
  /// Safe navigate back
  void navigateBack({dynamic result}) {
    BackNavigationHelper.safeNavigateBack(this, result: result);
  }

  /// Navigate back with refresh
  void navigateBackWithRefresh({VoidCallback? onRefresh, dynamic result}) {
    BackNavigationHelper.navigateBackWithRefresh(
      this,
      onRefresh: onRefresh,
      result: result,
    );
  }

  /// Navigate to screen with refresh on return
  Future<T?> navigateWithRefresh<T>(Widget screen, {VoidCallback? onReturn}) {
    return BackNavigationHelper.navigateWithRefresh<T>(
      this,
      screen,
      onReturn: onReturn,
    );
  }
}
