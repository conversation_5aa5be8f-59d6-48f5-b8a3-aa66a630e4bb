import '../services/appointment_service.dart';

class DatabaseSeeder {
  static Future<void> seedInitialData() async {
    try {
      print('🌱 Bắt đầu seed dữ liệu mẫu...');
      
      // Initialize connection
      final connected = await AppointmentService.initialize();
      if (!connected) {
        throw Exception('Không thể kết nối MongoDB');
      }

      // Seed departments
      await _seedDepartments();
      
      // Seed doctors (after departments are created)
      await _seedDoctors();
      
      // Seed schedules for doctors
      await _seedDoctorSchedules();
      
      print('✅ Seed dữ liệu mẫu thành công!');
      
    } catch (e) {
      print('❌ Lỗi seed dữ liệu: $e');
      rethrow;
    }
  }

  static Future<void> _seedDepartments() async {
    print('📋 Tạo các khoa...');
    
    final departments = [
      {
        'name': 'Khoa Nội',
        'description': 'Chuyên khoa điều trị các bệnh nội khoa: tim m<PERSON>ch, ti<PERSON><PERSON> hó<PERSON>, h<PERSON>, thận tiết niệu...',
      },
      {
        'name': '<PERSON><PERSON><PERSON>',
        'description': '<PERSON><PERSON><PERSON><PERSON> khoa phẫu thuật: ngoại tổng quát, ngoại thần kinh, ngoại lồng ngực...',
      },
      {
        'name': 'Khoa Sản Phụ khoa',
        'description': 'Chuyên khoa chăm sóc sức khỏe phụ nữ: sản khoa, phụ khoa, kế hoạch hóa gia đình...',
      },
      {
        'name': 'Khoa Nhi',
        'description': 'Chuyên khoa điều trị cho trẻ em từ sơ sinh đến 16 tuổi',
      },
      {
        'name': 'Khoa Mắt',
        'description': 'Chuyên khoa điều trị các bệnh về mắt: cận thị, viễn thị, đục thủy tinh thể...',
      },
      {
        'name': 'Khoa Tai Mũi Họng',
        'description': 'Chuyên khoa điều trị các bệnh về tai, mũi, họng',
      },
      {
        'name': 'Khoa Da liễu',
        'description': 'Chuyên khoa điều trị các bệnh về da: viêm da, dị ứng, nấm da...',
      },
      {
        'name': 'Khoa Răng Hàm Mặt',
        'description': 'Chuyên khoa điều trị các bệnh về răng, hàm, mặt',
      },
    ];

    for (final dept in departments) {
      try {
        final department = await AppointmentService.createDepartment(
          name: dept['name']!,
          description: dept['description']!,
        );
        if (department != null) {
          print('  ✅ Tạo khoa: ${department.name}');
        }
      } catch (e) {
        print('  ⚠️ Khoa ${dept['name']} có thể đã tồn tại: $e');
      }
    }
  }

  static Future<void> _seedDoctors() async {
    print('👨‍⚕️ Tạo bác sĩ...');
    
    // Get all departments first
    final departments = await AppointmentService.getAllDepartments();
    if (departments.isEmpty) {
      print('❌ Không có khoa nào để tạo bác sĩ');
      return;
    }

    // Sample doctors for each department
    final doctorsData = [
      // Khoa Nội
      {
        'fullName': 'Nguyễn Văn Minh',
        'specialization': 'Tim mạch',
        'departmentName': 'Khoa Nội',
        'title': 'Tiến sĩ',
        'experience': '15 năm',
        'education': 'Đại học Y Hà Nội',
        'phone': '0901234567',
        'email': '<EMAIL>',
        'description': 'Bác sĩ chuyên khoa tim mạch với 15 năm kinh nghiệm điều trị',
        'consultationFee': 300000.0,
        'availableDays': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      },
      {
        'fullName': 'Trần Thị Hương',
        'specialization': 'Tiêu hóa',
        'departmentName': 'Khoa Nội',
        'title': 'Thạc sĩ',
        'experience': '10 năm',
        'education': 'Đại học Y Dược TP.HCM',
        'phone': '0901234568',
        'email': '<EMAIL>',
        'description': 'Bác sĩ chuyên khoa tiêu hóa, điều trị các bệnh về dạ dày, ruột',
        'consultationFee': 250000.0,
        'availableDays': ['Monday', 'Wednesday', 'Friday'],
      },
      
      // Khoa Ngoại
      {
        'fullName': 'Lê Quang Dũng',
        'specialization': 'Ngoại tổng quát',
        'departmentName': 'Khoa Ngoại',
        'title': 'Phó Giáo sư',
        'experience': '20 năm',
        'education': 'Đại học Y Hà Nội',
        'phone': '0901234569',
        'email': '<EMAIL>',
        'description': 'Phó Giáo sư chuyên phẫu thuật ngoại tổng quát',
        'consultationFee': 500000.0,
        'availableDays': ['Tuesday', 'Thursday', 'Saturday'],
      },
      
      // Khoa Sản Phụ khoa
      {
        'fullName': 'Phạm Thị Lan',
        'specialization': 'Sản khoa',
        'departmentName': 'Khoa Sản Phụ khoa',
        'title': 'Tiến sĩ',
        'experience': '12 năm',
        'education': 'Đại học Y Thái Bình',
        'phone': '0901234570',
        'email': '<EMAIL>',
        'description': 'Bác sĩ chuyên khoa sản, theo dõi thai kỳ và sinh nở',
        'consultationFee': 350000.0,
        'availableDays': ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
      },
      
      // Khoa Nhi
      {
        'fullName': 'Hoàng Văn Tuấn',
        'specialization': 'Nhi khoa',
        'departmentName': 'Khoa Nhi',
        'title': 'Thạc sĩ',
        'experience': '8 năm',
        'education': 'Đại học Y Dược Huế',
        'phone': '0901234571',
        'email': '<EMAIL>',
        'description': 'Bác sĩ nhi khoa, chuyên điều trị cho trẻ em',
        'consultationFee': 200000.0,
        'availableDays': ['Monday', 'Wednesday', 'Friday', 'Saturday'],
      },
      
      // Khoa Mắt
      {
        'fullName': 'Vũ Thị Mai',
        'specialization': 'Nhãn khoa',
        'departmentName': 'Khoa Mắt',
        'title': 'Tiến sĩ',
        'experience': '14 năm',
        'education': 'Đại học Y Hà Nội',
        'phone': '0901234572',
        'email': '<EMAIL>',
        'description': 'Bác sĩ chuyên khoa mắt, phẫu thuật khúc xạ',
        'consultationFee': 400000.0,
        'availableDays': ['Tuesday', 'Thursday', 'Friday'],
      },
    ];

    for (final doctorData in doctorsData) {
      try {
        // Find department ID by name
        final department = departments.firstWhere(
          (dept) => dept.name == doctorData['departmentName'],
          orElse: () => departments.first,
        );

        final doctor = await AppointmentService.createDoctor(
          fullName: doctorData['fullName'] as String,
          specialization: doctorData['specialization'] as String,
          departmentId: department.id!,
          title: doctorData['title'] as String,
          experience: doctorData['experience'] as String,
          education: doctorData['education'] as String,
          phone: doctorData['phone'] as String,
          email: doctorData['email'] as String,
          description: doctorData['description'] as String,
          consultationFee: doctorData['consultationFee'] as double,
          availableDays: doctorData['availableDays'] as List<String>,
        );
        
        if (doctor != null) {
          print('  ✅ Tạo bác sĩ: ${doctor.title} ${doctor.fullName} - ${doctor.specialization}');
        }
      } catch (e) {
        print('  ⚠️ Bác sĩ ${doctorData['fullName']} có thể đã tồn tại: $e');
      }
    }
  }

  static Future<void> _seedDoctorSchedules() async {
    print('📅 Tạo lịch khám cho bác sĩ...');
    
    // Get all departments and their doctors
    final departments = await AppointmentService.getAllDepartments();
    
    for (final department in departments) {
      final doctors = await AppointmentService.getDoctorsByDepartment(department.id!);
      
      for (final doctor in doctors) {
        // Create schedules for available days
        for (final day in doctor.availableDays) {
          try {
            final schedule = await AppointmentService.createDoctorSchedule(
              doctorId: doctor.id!,
              dayOfWeek: day,
              startTime: '08:00',
              endTime: '17:00',
              breakStartTime: '12:00',
              breakEndTime: '13:00',
              slotDuration: 30,
              maxPatientsPerSlot: 1,
            );
            
            if (schedule != null) {
              print('  ✅ Tạo lịch: ${doctor.fullName} - $day');
            }
          } catch (e) {
            print('  ⚠️ Lịch ${doctor.fullName} - $day có thể đã tồn tại: $e');
          }
        }
      }
    }
  }

  /// Xóa tất cả dữ liệu (dùng để reset database)
  static Future<void> clearAllData() async {
    try {
      print('🗑️ Xóa tất cả dữ liệu...');
      
      final connected = await AppointmentService.initialize();
      if (!connected) {
        throw Exception('Không thể kết nối MongoDB');
      }

      // Note: This would require access to the collections directly
      // For now, we'll just print a warning
      print('⚠️ Chức năng xóa dữ liệu cần được implement trong AppointmentService');
      
    } catch (e) {
      print('❌ Lỗi xóa dữ liệu: $e');
      rethrow;
    }
  }
}
