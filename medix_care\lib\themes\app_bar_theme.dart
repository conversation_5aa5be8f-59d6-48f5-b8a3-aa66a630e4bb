import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

/// Consistent AppBar theme for the entire application
class AppBarTheme {
  /// Default AppBar theme data
  static AppBarTheme get defaultTheme => AppBarTheme._();

  AppBarTheme._();

  /// Creates a consistent AppBar with default styling
  static AppBar create({
    required String title,
    List<Widget>? actions,
    bool automaticallyImplyLeading = true,
    VoidCallback? onBackPressed,
    bool centerTitle = false,
    double? elevation,
    Widget? leading,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: centerTitle,
      elevation: elevation ?? 2,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading ?? (automaticallyImplyLeading && onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: onBackPressed,
              tooltip: 'Quay lại',
            )
          : null),
      actions: actions,
      iconTheme: const IconThemeData(
        color: AppConstants.whiteText,
        size: 24,
      ),
      actionsIconTheme: const IconThemeData(
        color: AppConstants.whiteText,
        size: 24,
      ),
    );
  }

  /// Creates AppBar for payment screens with special styling
  static AppBar createPaymentAppBar({
    required String title,
    VoidCallback? onBackPressed,
    bool showBackButton = true,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: true,
      elevation: 0,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton && onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: onBackPressed,
              tooltip: 'Quay lại',
            )
          : showBackButton ? null : const SizedBox.shrink(),
      actions: [
        if (showBackButton)
          IconButton(
            icon: const Icon(Icons.security),
            onPressed: () {}, // Security indicator - no action needed
            tooltip: 'Bảo mật',
          ),
      ],
    );
  }

  /// Creates AppBar for form screens
  static AppBar createFormAppBar({
    required String title,
    VoidCallback? onBackPressed,
    VoidCallback? onSave,
    bool hasUnsavedChanges = false,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: false,
      elevation: 2,
      automaticallyImplyLeading: true,
      leading: onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: onBackPressed,
              tooltip: 'Quay lại',
            )
          : null,
      actions: [
        if (onSave != null)
          TextButton(
            onPressed: onSave,
            child: Text(
              'Lưu',
              style: TextStyle(
                color: hasUnsavedChanges 
                    ? AppConstants.whiteText 
                    : AppConstants.whiteText.withOpacity(0.6),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// Creates AppBar for authentication screens
  static AppBar createAuthAppBar({
    required String title,
    bool showBackButton = true,
    VoidCallback? onBackPressed,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: true,
      elevation: 1,
      automaticallyImplyLeading: showBackButton,
      leading: showBackButton && onBackPressed != null
          ? IconButton(
              icon: const Icon(Icons.arrow_back_ios),
              onPressed: onBackPressed,
              tooltip: 'Quay lại',
            )
          : showBackButton ? null : const SizedBox.shrink(),
    );
  }

  /// Creates AppBar for result screens (no back button)
  static AppBar createResultAppBar({
    required String title,
    List<Widget>? actions,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 18,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: true,
      elevation: 0,
      automaticallyImplyLeading: false,
      actions: actions,
    );
  }

  /// Creates AppBar for home screen with drawer
  static AppBar createHomeAppBar({
    required String title,
    List<Widget>? actions,
  }) {
    return AppBar(
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 20,
        ),
      ),
      backgroundColor: AppConstants.primaryBlue,
      foregroundColor: AppConstants.whiteText,
      centerTitle: false,
      elevation: 2,
      actions: actions,
    );
  }


}

/// Extension to add AppBar creation methods to BuildContext
extension AppBarExtension on BuildContext {
  /// Create default AppBar
  AppBar createAppBar({
    required String title,
    List<Widget>? actions,
    bool automaticallyImplyLeading = true,
    VoidCallback? onBackPressed,
    bool centerTitle = false,
  }) {
    return AppBarTheme.create(
      title: title,
      actions: actions,
      automaticallyImplyLeading: automaticallyImplyLeading,
      onBackPressed: onBackPressed,
      centerTitle: centerTitle,
    );
  }

  /// Create payment AppBar
  AppBar createPaymentAppBar({
    required String title,
    VoidCallback? onBackPressed,
    bool showBackButton = true,
  }) {
    return AppBarTheme.createPaymentAppBar(
      title: title,
      onBackPressed: onBackPressed,
      showBackButton: showBackButton,
    );
  }

  /// Create form AppBar
  AppBar createFormAppBar({
    required String title,
    VoidCallback? onBackPressed,
    VoidCallback? onSave,
    bool hasUnsavedChanges = false,
  }) {
    return AppBarTheme.createFormAppBar(
      title: title,
      onBackPressed: onBackPressed,
      onSave: onSave,
      hasUnsavedChanges: hasUnsavedChanges,
    );
  }

  /// Create auth AppBar
  AppBar createAuthAppBar({
    required String title,
    bool showBackButton = true,
    VoidCallback? onBackPressed,
  }) {
    return AppBarTheme.createAuthAppBar(
      title: title,
      showBackButton: showBackButton,
      onBackPressed: onBackPressed,
    );
  }
}
