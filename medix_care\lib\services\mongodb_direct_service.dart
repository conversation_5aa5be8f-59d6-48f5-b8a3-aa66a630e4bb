import 'dart:convert';
import 'package:mongo_dart/mongo_dart.dart';
import 'package:crypto/crypto.dart';
import '../models/user.dart';
import '../config/mongodb_direct_config.dart';
import 'jwt_service.dart';

class MongoDBDirectService {
  static Db? _db;
  static DbCollection? _usersCollection;

  /// Initialize MongoDB connection
  static Future<bool> initialize() async {
    try {
      _db = await Db.create(MongoDBConfig.connectionString);
      await _db!.open();
      
      _usersCollection = _db!.collection(MongoDBConfig.usersCollection);
      
      print('✅ MongoDB connected successfully!');
      print('Database: ${MongoDBConfig.database}');
      print('Collection: ${MongoDBConfig.usersCollection}');
      
      return true;
    } catch (e) {
      print('❌ MongoDB connection failed: $e');
      return false;
    }
  }

  /// Close MongoDB connection
  static Future<void> close() async {
    await _db?.close();
    _db = null;
    _usersCollection = null;
  }

  /// Hash password
  static String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Clean ObjectId format from string 
  static String _cleanObjectId(String objectIdString) {
    if (objectIdString.isEmpty) {
      return objectIdString;
    }
    
    // Handle format: ObjectId("hexadecimal")
    if (objectIdString.startsWith('ObjectId("') && objectIdString.endsWith('")')) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    // Handle format: ObjectId('hexadecimal')
    if (objectIdString.startsWith("ObjectId('") && objectIdString.endsWith("')")) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    return objectIdString;
  }

  /// Create new user
  static Future<User?> createUser({
    required String fullName,
    required String gender,
    required int birthYear,
    required String province,
    required String provinceCode,
    required String ward,
    required String wardCode,
    required String phone,
    required String email,
    String? password,
    bool isPhoneVerified = false,
  }) async {
    try {
      if (_usersCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Check if user already exists
      final existingUser = await getUserByEmail(email);
      if (existingUser != null) {
        throw Exception('Email đã được sử dụng');
      }

      final user = User(
        fullName: fullName,
        gender: gender,
        birthYear: birthYear,
        province: province,
        provinceCode: provinceCode,
        ward: ward,
        wardCode: wardCode,
        phone: phone,
        email: email,
        passwordHash: password != null ? _hashPassword(password) : null,
        createdAt: DateTime.now(),
        isActive: true,
        isPhoneVerified: isPhoneVerified,
      );

      final result = await _usersCollection!.insertOne(user.toJson());
      
      if (result.isSuccess) {
        print('✅ User created successfully: ${result.id}');
        // Extract hex string from ObjectId
        final userId = result.id!.toHexString();
        return user.copyWith(id: userId);
      } else {
        throw Exception('Failed to create user');
      }
    } catch (e) {
      print('❌ Error creating user: $e');
      throw Exception('Lỗi tạo tài khoản: $e');
    }
  }

  /// Get user by email
  static Future<User?> getUserByEmail(String email) async {
    try {
      if (_usersCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final result = await _usersCollection!.findOne(where.eq('email', email));
      
      if (result != null) {
        return User.fromJson(result);
      }
      return null;
    } catch (e) {
      print('❌ Error getting user by email: $e');
      return null;
    }
  }

  /// Get user by phone number
  static Future<User?> getUserByPhone(String phone) async {
    try {
      if (_usersCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      final result = await _usersCollection!.findOne(where.eq('phone', phone));
      
      if (result != null) {
        return User.fromJson(result);
      }
      return null;
    } catch (e) {
      print('❌ Error getting user by phone: $e');
      return null;
    }
  }

  /// Get user by ID
  static Future<User?> getUserById(String userId) async {
    try {
      if (_usersCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Clean userId if it contains ObjectId prefix
      String cleanUserId = _cleanObjectId(userId);

      final objectId = ObjectId.fromHexString(cleanUserId);
      final result = await _usersCollection!.findOne(where.id(objectId));
      
      if (result != null) {
        return User.fromJson(result);
      }
      return null;
    } catch (e) {
      print('❌ Error getting user by ID: $e');
      print('❌ UserId format: $userId');
      return null;
    }
  }

  /// Update user
  static Future<bool> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      if (_usersCollection == null) {
        throw Exception('MongoDB chưa được khởi tạo');
      }

      // Hash password if it's being updated
      if (updates.containsKey('password')) {
        final plainPassword = updates['password'];
        if (plainPassword != null && plainPassword is String) {
          updates['passwordHash'] = _hashPassword(plainPassword);
          updates.remove('password'); // Remove plain password, keep only hash
          print('🔐 Password hashed for update');
        }
      }

      updates['updatedAt'] = DateTime.now().toIso8601String();
      
      // Clean userId if it contains ObjectId prefix
      String cleanUserId = _cleanObjectId(userId);
      
      final objectId = ObjectId.fromHexString(cleanUserId);
      
      // Create modifier for all updates
      var modifier = modify;
      for (final entry in updates.entries) {
        modifier = modifier.set(entry.key, entry.value);
      }
      
      final result = await _usersCollection!.updateOne(
        where.id(objectId),
        modifier
      );

      print('✅ User updated successfully: ${result.isSuccess}');
      return result.isSuccess;
    } catch (e) {
      print('❌ Error updating user: $e');
      print('❌ UserId format: $userId');
      return false;
    }
  }

  /// Authenticate user by email or phone number
  static Future<Map<String, dynamic>?> authenticateUser(String emailOrPhone, String password) async {
    try {
      User? user;
      
      // Check if input is email or phone number
      if (_isEmail(emailOrPhone)) {
        user = await getUserByEmail(emailOrPhone);
      } else if (_isPhoneNumber(emailOrPhone)) {
        user = await getUserByPhone(emailOrPhone);
      } else {
        throw Exception('Định dạng email hoặc số điện thoại không hợp lệ');
      }
      
      if (user == null) {
        throw Exception('Tài khoản không tồn tại');
      }

      final hashedPassword = _hashPassword(password);
      if (user.passwordHash != hashedPassword) {
        throw Exception('Mật khẩu không đúng');
      }

      // Generate JWT tokens
      final accessToken = JWTService.generateAccessToken(user);
      final refreshToken = JWTService.generateRefreshToken(user);

      // Update refresh token in database
      await updateUser(user.id!, {
        'refreshToken': refreshToken,
        'refreshTokenExpiry': DateTime.now().add(const Duration(days: 7)).toIso8601String(),
      });

      // Save tokens locally
      await JWTService.saveTokens(accessToken, refreshToken, user);

      return {
        'user': user,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
      };
    } catch (e) {
      print('❌ Error authenticating user: $e');
      throw Exception('Lỗi đăng nhập: $e');
    }
  }

  /// Check if string is email format
  static bool _isEmail(String input) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(input);
  }

  /// Check if string is phone number format
  static bool _isPhoneNumber(String input) {
    return RegExp(r'^[0-9]{10,11}$').hasMatch(input);
  }

  /// Register user with password
  static Future<Map<String, dynamic>?> registerUser({
    required String fullName,
    required String gender,
    required int birthYear,
    required String province,
    required String provinceCode,
    required String ward,
    required String wardCode,
    required String phone,
    required String email,
    required String password,
    bool isPhoneVerified = false,
  }) async {
    try {
      final user = await createUser(
        fullName: fullName,
        gender: gender,
        birthYear: birthYear,
        province: province,
        provinceCode: provinceCode,
        ward: ward,
        wardCode: wardCode,
        phone: phone,
        email: email,
        password: password,
        isPhoneVerified: isPhoneVerified,
      );

      if (user == null) {
        throw Exception('Không thể tạo tài khoản');
      }

      // Generate JWT tokens
      final accessToken = JWTService.generateAccessToken(user);
      final refreshToken = JWTService.generateRefreshToken(user);

      // Update refresh token in database
      await updateUser(user.id!, {
        'refreshToken': refreshToken,
        'refreshTokenExpiry': DateTime.now().add(const Duration(days: 7)).toIso8601String(),
      });

      // Save tokens locally
      await JWTService.saveTokens(accessToken, refreshToken, user);

      return {
        'user': user,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
      };
    } catch (e) {
      print('❌ Error registering user: $e');
      throw Exception('Lỗi đăng ký: $e');
    }
  }

  /// Test MongoDB connection
  static Future<bool> testConnection() async {
    try {
      if (_db == null) {
        await initialize();
      }
      
      // Test with a simple operation
      final collections = await _db!.getCollectionNames();
      print('✅ MongoDB connection test successful');
      print('Available collections: ${collections.length}');
      
      return true;
    } catch (e) {
      print('❌ MongoDB connection test failed: $e');
      return false;
    }
  }
}
