import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/province.dart';
import '../models/ward.dart';

class LocationService {
  static const String _baseUrl = 'https://34tinhthanh.com/api';

  // L<PERSON>y danh sách tỉnh/thành phố
  static Future<List<Province>> getProvinces() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/provinces'),
        headers: {'Content-Type': 'application/json'},
      );

      print('Provinces API Response Status: ${response.statusCode}');
      print('Provinces API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Province.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load provinces: ${response.statusCode}');
      }
    } catch (e) {
      print('Error fetching provinces: $e');
      throw Exception('Error fetching provinces: $e');
    }
  }

  // L<PERSON>y danh sách phường/xã theo mã tỉnh
  static Future<List<Ward>> getWardsByProvinceCode(String provinceCode) async {
    try {
      final url = '$_baseUrl/wards?province_code=$provinceCode';
      print('Wards API URL: $url');
      
      final response = await http.get(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
      );

      print('Wards API Response Status: ${response.statusCode}');
      print('Wards API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        return data.map((json) => Ward.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load wards: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('Error fetching wards: $e');
      throw Exception('Error fetching wards: $e');
    }
  }
}
