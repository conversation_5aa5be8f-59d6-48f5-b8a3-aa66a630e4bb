import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../lib/utils/navigation_middleware.dart';
import '../../lib/services/jwt_service.dart';
import '../../lib/models/user.dart';

void main() {
  group('NavigationMiddleware Tests', () {
    late User testUser;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      
      testUser = User(
        id: 'test-user-id',
        fullName: 'Test User',
        email: '<EMAIL>',
        phone: '0123456789',
        gender: 'Nam',
        birthYear: 1990,
        province: 'Hà Nội',
        provinceCode: '01',
        ward: 'Ba Đình',
        wardCode: '001',
        isEmailVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    testWidgets('should allow navigation when user is authenticated', (WidgetTester tester) async {
      // Setup authenticated user
      final accessToken = JWTService.generateAccessToken(testUser);
      final refreshToken = JWTService.generateRefreshToken(testUser);
      await JWTService.saveTokens(accessToken, refreshToken, testUser);

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  final canNavigate = await NavigationMiddleware.checkAuthBeforeNavigation(context);
                  if (canNavigate) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const Scaffold(
                          body: Text('Next Screen'),
                        ),
                      ),
                    );
                  }
                },
                child: const Text('Navigate'),
              ),
            ),
          ),
        ),
      );

      // Tap the navigate button
      await tester.tap(find.text('Navigate'));
      await tester.pumpAndSettle();

      // Should navigate to next screen
      expect(find.text('Next Screen'), findsOneWidget);
    });

    testWidgets('should show auth dialog when user is not authenticated', (WidgetTester tester) async {
      // Clear any existing tokens
      await JWTService.logout();

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  await NavigationMiddleware.checkAuthBeforeNavigation(context);
                },
                child: const Text('Navigate'),
              ),
            ),
          ),
        ),
      );

      // Tap the navigate button
      await tester.tap(find.text('Navigate'));
      await tester.pumpAndSettle();

      // Should show auth expired dialog
      expect(find.text('Phiên đăng nhập hết hạn'), findsOneWidget);
      expect(find.text('Để sau'), findsOneWidget);
      expect(find.text('Đăng nhập'), findsOneWidget);
    });

    testWidgets('should navigate with auth check successfully', (WidgetTester tester) async {
      // Setup authenticated user
      final accessToken = JWTService.generateAccessToken(testUser);
      final refreshToken = JWTService.generateRefreshToken(testUser);
      await JWTService.saveTokens(accessToken, refreshToken, testUser);

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  await NavigationMiddleware.navigateWithAuthCheck(
                    context,
                    const Scaffold(body: Text('Authenticated Screen')),
                    settings: const RouteSettings(name: '/authenticated'),
                  );
                },
                child: const Text('Navigate with Auth'),
              ),
            ),
          ),
        ),
      );

      // Tap the navigate button
      await tester.tap(find.text('Navigate with Auth'));
      await tester.pumpAndSettle();

      // Should navigate to authenticated screen
      expect(find.text('Authenticated Screen'), findsOneWidget);
    });

    testWidgets('should handle safe back navigation correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        body: ElevatedButton(
                          onPressed: () {
                            NavigationMiddleware.safeNavigateBack(context);
                          },
                          child: const Text('Go Back'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('Navigate Forward'),
              ),
            ),
          ),
        ),
      );

      // Navigate forward
      await tester.tap(find.text('Navigate Forward'));
      await tester.pumpAndSettle();

      // Should be on second screen
      expect(find.text('Go Back'), findsOneWidget);

      // Navigate back
      await tester.tap(find.text('Go Back'));
      await tester.pumpAndSettle();

      // Should be back on first screen
      expect(find.text('Navigate Forward'), findsOneWidget);
    });

    testWidgets('should handle safe back navigation at root', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  NavigationMiddleware.safeNavigateBack(context);
                },
                child: const Text('Go Back at Root'),
              ),
            ),
          ),
        ),
      );

      // Try to navigate back at root
      await tester.tap(find.text('Go Back at Root'));
      await tester.pumpAndSettle();

      // Should show snackbar message instead of exiting
      expect(find.text('Đã ở trang chủ'), findsOneWidget);
      expect(find.text('Go Back at Root'), findsOneWidget); // Still on same screen
    });

    testWidgets('should navigate back to specific route', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  // Navigate to first screen
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        body: ElevatedButton(
                          onPressed: () {
                            // Navigate to second screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => Scaffold(
                                  body: ElevatedButton(
                                    onPressed: () {
                                      // Navigate back to home
                                      NavigationMiddleware.navigateBackToRoute(context, '/');
                                    },
                                    child: const Text('Back to Home'),
                                  ),
                                ),
                                settings: const RouteSettings(name: '/second'),
                              ),
                            );
                          },
                          child: const Text('To Second'),
                        ),
                      ),
                      settings: const RouteSettings(name: '/first'),
                    ),
                  );
                },
                child: const Text('Start Navigation'),
              ),
            ),
          ),
        ),
      );

      // Start navigation chain
      await tester.tap(find.text('Start Navigation'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('To Second'));
      await tester.pumpAndSettle();

      // Should be on second screen
      expect(find.text('Back to Home'), findsOneWidget);

      // Navigate back to home
      await tester.tap(find.text('Back to Home'));
      await tester.pumpAndSettle();

      // Should be back at home
      expect(find.text('Start Navigation'), findsOneWidget);
    });

    testWidgets('should handle navigation with error handling', (WidgetTester tester) async {
      // Setup authenticated user for this test
      final accessToken = JWTService.generateAccessToken(testUser);
      final refreshToken = JWTService.generateRefreshToken(testUser);
      await JWTService.saveTokens(accessToken, refreshToken, testUser);

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  await NavigationMiddleware.navigateWithErrorHandling(
                    context,
                    const Scaffold(body: Text('Error Test Screen')),
                    errorMessage: 'Custom error message',
                  );
                },
                child: const Text('Navigate with Error Handling'),
              ),
            ),
          ),
        ),
      );

      // This should work normally with authenticated user
      await tester.tap(find.text('Navigate with Error Handling'));
      await tester.pumpAndSettle();

      expect(find.text('Error Test Screen'), findsOneWidget);
    });

    testWidgets('should clear navigation stack safely', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  // Navigate through multiple screens
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        body: ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => Scaffold(
                                  body: ElevatedButton(
                                    onPressed: () {
                                      // Clear stack and go to home
                                      NavigationMiddleware.clearStackSafely(
                                        context,
                                        const Scaffold(body: Text('New Home')),
                                      );
                                    },
                                    child: const Text('Clear Stack'),
                                  ),
                                ),
                              ),
                            );
                          },
                          child: const Text('To Third'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('Start Deep Navigation'),
              ),
            ),
          ),
        ),
      );

      // Navigate deep
      await tester.tap(find.text('Start Deep Navigation'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('To Third'));
      await tester.pumpAndSettle();

      // Clear stack
      await tester.tap(find.text('Clear Stack'));
      await tester.pumpAndSettle();

      // Should be on new home screen
      expect(find.text('New Home'), findsOneWidget);
      
      // Should not be able to go back
      expect(find.text('Start Deep Navigation'), findsNothing);
      expect(find.text('To Third'), findsNothing);
      expect(find.text('Clear Stack'), findsNothing);
    });
  });
}
