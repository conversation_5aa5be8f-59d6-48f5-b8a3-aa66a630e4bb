import 'package:flutter/material.dart';
import '../models/doctor.dart';
import '../models/department.dart';
import '../models/doctor_schedule.dart';
import '../services/appointment_service.dart';
import '../services/jwt_service.dart';
import '../constants/appointment_constants.dart';
<<<<<<< HEAD
import '../utils/id_formatter.dart';
import 'payment_method_screen.dart';
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697

class BookAppointmentScreen extends StatefulWidget {
  final Doctor doctor;
  final Department department;
  final DateTime? initialDate;
  final String? initialTimeSlot;

  const BookAppointmentScreen({
    super.key,
    required this.doctor,
    required this.department,
    this.initialDate,
    this.initialTimeSlot,
  });

  @override
  State<BookAppointmentScreen> createState() => _BookAppointmentScreenState();
}

class _BookAppointmentScreenState extends State<BookAppointmentScreen> {
  DateTime? selectedDate;
  String? selectedTime;
  List<DoctorSchedule> schedules = [];
  List<String> availableTimeSlots = [];
  bool isLoading = true;
  String? error;

  // Form controllers
  final _symptomsController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Set initial values from filter
    selectedDate = widget.initialDate;
    selectedTime = widget.initialTimeSlot;
    _loadDoctorSchedules();
  }

  @override
  void dispose() {
    _symptomsController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadDoctorSchedules() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      final loadedSchedules = await AppointmentService.getDoctorSchedules(widget.doctor.id!);
      
      setState(() {
        schedules = loadedSchedules;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  void _onDateSelected(DateTime date) {
    setState(() {
      selectedDate = date;
      selectedTime = null; // Reset selected time
    });
    _generateTimeSlots(date);
  }

  void _generateTimeSlots(DateTime date) {
    // Get day of week from date
    final dayName = _getDayName(date.weekday);
    
    // Find schedule for this day
    final daySchedule = schedules.where((s) => s.dayOfWeek == dayName).toList();
    
    if (daySchedule.isNotEmpty) {
      final schedule = daySchedule.first;
      setState(() {
        availableTimeSlots = schedule.generateTimeSlots();
      });
    } else {
      setState(() {
        availableTimeSlots = [];
      });
    }
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Monday';
    }
  }

  bool _isDateAvailable(DateTime date) {
    final dayName = _getDayName(date.weekday);
    return widget.doctor.availableDays.contains(dayName) && 
           date.isAfter(DateTime.now().subtract(const Duration(days: 1)));
  }

  Future<void> _bookAppointment() async {
    if (selectedDate == null || selectedTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn ngày và giờ khám'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_symptomsController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng mô tả triệu chứng'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    try {
      // Get current user
      final user = await JWTService.getCurrentUser();
      if (user == null) {
<<<<<<< HEAD
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Vui lòng đăng nhập để đặt lịch khám'),
              backgroundColor: Colors.red,
            ),
          );
        }
=======
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Vui lòng đăng nhập để đặt lịch khám'),
            backgroundColor: Colors.red,
          ),
        );
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
        return;
      }

      // Debug log before creating appointment
      print('🔍 DEBUG - Creating appointment:');
      print('  User ID: "${user.id}"');
      print('  User Name: ${user.fullName}');
      print('  User Email: ${user.email}');
      print('  Doctor ID: ${widget.doctor.id}');
      print('  Department ID: ${widget.department.id}');
      print('  Date: $selectedDate');
      print('  Time: $selectedTime');

      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Đang đặt lịch khám...'),
            ],
          ),
        ),
      );

      final appointment = await AppointmentService.createAppointment(
        patientId: user.id!, // Use real user ID
        doctorId: widget.doctor.id!,
        departmentId: widget.department.id!,
        appointmentDate: selectedDate!,
        appointmentTime: selectedTime!,
        symptoms: _symptomsController.text.trim(),
        notes: _notesController.text.trim(),
        consultationFee: widget.doctor.consultationFee,
      );

      // Debug log
      print('🔍 DEBUG - Appointment created:');
      print('  Patient ID: ${user.id}');
<<<<<<< HEAD
      print('  Appointment ID: ${IdFormatter.formatId(appointment?.id, short: false)}');
=======
      print('  Appointment ID: ${appointment?.id}');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      print('  Appointment Patient ID: ${appointment?.patientId}');
      print('  Doctor ID: ${appointment?.doctorId}');
      print('  Date: ${appointment?.appointmentDate}');
      print('  Time: ${appointment?.appointmentTime}');
<<<<<<< HEAD
      
      // Chuyển đến trang phương thức thanh toán
      if (mounted && appointment != null) {
        Navigator.of(context).pop(); // Đóng dialog loading
        
        Navigator.pushReplacement(
          context, 
          MaterialPageRoute(
            builder: (context) => PaymentMethodScreen(
              appointment: appointment,
              doctor: widget.doctor,
              doctorName: widget.doctor.fullName,
            ),
          ),
        );
        return;
      }
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697

      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        
        if (appointment != null) {
          // Show success dialog
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Đặt lịch thành công'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
<<<<<<< HEAD
                  Text('Mã lịch khám: ${IdFormatter.formatId(appointment.id)}'),
=======
                  Text('Mã lịch khám: ${appointment.id}'),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                  const SizedBox(height: 8),
                  Text('Bác sĩ: ${widget.doctor.title} ${widget.doctor.fullName}'),
                  Text('Khoa: ${widget.department.name}'),
                  Text('Ngày: ${AppointmentConstants.formatDate(selectedDate!)}'),
                  Text('Giờ: $selectedTime'),
                  Text('Phí khám: ${AppointmentConstants.formatCurrency(widget.doctor.consultationFee)}'),
                  const SizedBox(height: 8),
                  const Text(
                    'Vui lòng đến đúng giờ và mang theo CCCD/CMND',
                    style: TextStyle(
                      fontSize: 12,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context); // Close dialog
                    Navigator.pop(context); // Return to previous screen
                  },
                  child: const Text('Đóng'),
                ),
              ],
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.pop(context); // Close loading dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đặt lịch: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Đặt lịch khám'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? _buildErrorState()
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDoctorInfo(),
                      const SizedBox(height: 24),
                      _buildDateSelector(),
                      const SizedBox(height: 24),
                      _buildTimeSelector(),
                      const SizedBox(height: 24),
                      _buildSymptomForm(),
                      const SizedBox(height: 32),
                      _buildBookButton(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text('Có lỗi xảy ra: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadDoctorSchedules,
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  Widget _buildDoctorInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
<<<<<<< HEAD
                  backgroundColor: Colors.blue.withValues(alpha: 0.2),
=======
                  backgroundColor: Colors.blue.withOpacity(0.2),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                  child: Text(
                    widget.doctor.fullName.split(' ').last[0].toUpperCase(),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${widget.doctor.title} ${widget.doctor.fullName}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Chuyên khoa: ${widget.doctor.specialization}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                      Text(
                        'Khoa: ${widget.department.name}',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.monetization_on, color: Colors.green, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Phí khám: ${AppointmentConstants.formatCurrency(widget.doctor.consultationFee)}',
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              widget.doctor.description,
              style: TextStyle(color: Colors.grey[700]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Chọn ngày khám',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 14, // Show next 14 days
                itemBuilder: (context, index) {
                  final date = DateTime.now().add(Duration(days: index + 1));
                  final isAvailable = _isDateAvailable(date);
                  final isSelected = selectedDate != null &&
                      selectedDate!.day == date.day &&
                      selectedDate!.month == date.month &&
                      selectedDate!.year == date.year;

                  return Container(
                    width: 80,
                    margin: const EdgeInsets.only(right: 8),
                    child: InkWell(
                      onTap: isAvailable ? () => _onDateSelected(date) : null,
                      child: Container(
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).primaryColor
                              : isAvailable
                                  ? Colors.grey[100]
                                  : Colors.grey[300],
                          borderRadius: BorderRadius.circular(8),
                          border: isSelected
                              ? Border.all(
                                  color: Theme.of(context).primaryColor,
                                  width: 2,
                                )
                              : null,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              AppointmentConstants.dayToVietnamese(_getDayName(date.weekday)),
                              style: TextStyle(
                                fontSize: 12,
                                color: isSelected ? Colors.white : 
                                       isAvailable ? Colors.black87 : Colors.grey,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${date.day}/${date.month}',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : 
                                       isAvailable ? Colors.black87 : Colors.grey,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSelector() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Chọn giờ khám',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (selectedDate == null)
              const Text(
                'Vui lòng chọn ngày trước',
                style: TextStyle(color: Colors.grey),
              )
            else if (availableTimeSlots.isEmpty)
              const Text(
                'Không có giờ khám trong ngày này',
                style: TextStyle(color: Colors.orange),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: availableTimeSlots.map((time) {
                  final isSelected = selectedTime == time;
                  return InkWell(
                    onTap: () {
                      setState(() {
                        selectedTime = time;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Theme.of(context).primaryColor
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(20),
                        border: isSelected
                            ? Border.all(color: Theme.of(context).primaryColor)
                            : Border.all(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        time,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black87,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSymptomForm() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thông tin khám',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _symptomsController,
              decoration: const InputDecoration(
                labelText: 'Triệu chứng *',
                hintText: 'Mô tả triệu chứng bạn đang gặp phải',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Ghi chú thêm',
                hintText: 'Thông tin bổ sung (nếu có)',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBookButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _bookAppointment,
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: const Text(
          'Xác nhận đặt lịch',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }
}
