class DoctorSchedule {
  final String? id;
  final String doctorId;
  final String dayOfWeek; // Thứ trong tuần (Monday, Tuesday, ...)
  final String startTime; // <PERSON><PERSON><PERSON> bắt đầu (08:00)
  final String endTime; // <PERSON><PERSON><PERSON> kết thúc (17:00)
  final String breakStartTime; // Giờ nghỉ trưa bắt đầu (12:00)
  final String breakEndTime; // Giờ nghỉ trưa kết thúc (13:00)
  final int slotDuration; // Thời gian mỗi slot khám (phút)
  final int maxPatientsPerSlot; // Số bệnh nhân tối đa mỗi slot
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  DoctorSchedule({
    this.id,
    required this.doctorId,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.breakStartTime,
    required this.breakEndTime,
    this.slotDuration = 30, // Mặc định 30 phút/slot
    this.maxPatientsPerSlot = 1, // Mặc định 1 bệnh nhân/slot
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory DoctorSchedule.fromJson(Map<String, dynamic> json) {
    // Handle ID parsing more safely
    String? scheduleId;
    if (json['_id'] != null) {
      if (json['_id'] is Map) {
        scheduleId = json['_id']['\$oid'];
      } else {
        scheduleId = json['_id'].toString();
      }
    }

    return DoctorSchedule(
      id: scheduleId,
      doctorId: json['doctorId'] ?? '',
      dayOfWeek: json['dayOfWeek'] ?? '',
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      breakStartTime: json['breakStartTime'] ?? '',
      breakEndTime: json['breakEndTime'] ?? '',
      slotDuration: json['slotDuration'] ?? 30,
      maxPatientsPerSlot: json['maxPatientsPerSlot'] ?? 1,
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'doctorId': doctorId,
      'dayOfWeek': dayOfWeek,
      'startTime': startTime,
      'endTime': endTime,
      'breakStartTime': breakStartTime,
      'breakEndTime': breakEndTime,
      'slotDuration': slotDuration,
      'maxPatientsPerSlot': maxPatientsPerSlot,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  /// Tạo danh sách các time slot khả dụng trong ngày
  List<String> generateTimeSlots() {
    List<String> slots = [];
    
    // Parse time strings to DateTime objects for calculation
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    final breakStart = _parseTime(breakStartTime);
    final breakEnd = _parseTime(breakEndTime);
    
    DateTime current = start;
    
    while (current.isBefore(end)) {
      // Skip break time
      if (current.isBefore(breakStart) || current.isAtOrAfter(breakEnd)) {
        slots.add(_formatTime(current));
      }
      
      current = current.add(Duration(minutes: slotDuration));
    }
    
    return slots;
  }

  DateTime _parseTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2024, 1, 1, hour, minute);
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  DoctorSchedule copyWith({
    String? id,
    String? doctorId,
    String? dayOfWeek,
    String? startTime,
    String? endTime,
    String? breakStartTime,
    String? breakEndTime,
    int? slotDuration,
    int? maxPatientsPerSlot,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DoctorSchedule(
      id: id ?? this.id,
      doctorId: doctorId ?? this.doctorId,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      breakStartTime: breakStartTime ?? this.breakStartTime,
      breakEndTime: breakEndTime ?? this.breakEndTime,
      slotDuration: slotDuration ?? this.slotDuration,
      maxPatientsPerSlot: maxPatientsPerSlot ?? this.maxPatientsPerSlot,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() => '$dayOfWeek: $startTime - $endTime';
}

extension DateTimeExtensions on DateTime {
  bool isAtOrAfter(DateTime other) {
    return isAtSameMomentAs(other) || isAfter(other);
  }
}
