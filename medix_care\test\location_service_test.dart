import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/services/location_service.dart';

void main() {
  group('Location Service Tests', () {
    test('should fetch provinces', () async {
      try {
        final provinces = await LocationService.getProvinces();
        print('Provinces count: ${provinces.length}');
        if (provinces.isNotEmpty) {
          print('First province: ${provinces.first.name} - ${provinces.first.provinceCode}');
        }
        expect(provinces, isNotEmpty);
      } catch (e) {
        print('Error: $e');
        fail('Failed to fetch provinces: $e');
      }
    });

    test('should fetch wards for Hanoi', () async {
      try {
        final wards = await LocationService.getWardsByProvinceCode('01');
        print('Wards count: ${wards.length}');
        if (wards.isNotEmpty) {
          print('First ward: ${wards.first.name} - ${wards.first.wardCode}');
        }
        expect(wards, isNotEmpty);
      } catch (e) {
        print('Error: $e');
        fail('Failed to fetch wards: $e');
      }
    });
  });
}
