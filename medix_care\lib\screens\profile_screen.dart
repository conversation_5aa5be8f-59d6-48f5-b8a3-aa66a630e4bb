import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../models/user.dart';
import '../models/province.dart';
import '../models/ward.dart';
import '../services/auth_service.dart';
import '../services/jwt_service.dart';
import '../services/mongodb_direct_service.dart';
import '../services/location_service.dart';
<<<<<<< HEAD
import '../utils/id_formatter.dart';
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
import 'email_verification_screen.dart';

class ProfileScreen extends StatefulWidget {
  final User user;

  const ProfileScreen({
    super.key,
    required this.user,
  });

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  late TextEditingController _fullNameController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  final _passwordController = TextEditingController();
  
  // Form values
  late String _selectedGender;
  late int _selectedBirthYear;
  
  // Location data
  List<Province> _provinces = [];
  List<Ward> _wards = [];
  Province? _selectedProvince;
  Ward? _selectedWard;
  bool _loadingProvinces = true;
  bool _loadingWards = false;
  
  bool _isLoading = false;
  bool _isEditing = false;
  bool _obscurePassword = true;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadProvinces();
  }

  void _initializeControllers() {
    _fullNameController = TextEditingController(text: widget.user.fullName);
    _phoneController = TextEditingController(text: widget.user.phone);
    _emailController = TextEditingController(text: widget.user.email);
    _selectedGender = widget.user.gender;
    _selectedBirthYear = widget.user.birthYear;
  }

  Future<void> _loadProvinces() async {
    try {
      final provinces = await LocationService.getProvinces();
      setState(() {
        _provinces = provinces;
        _loadingProvinces = false;
        
        // Find current province if exists
        _selectedProvince = provinces.firstWhere(
          (p) => p.name == widget.user.province,
          orElse: () => provinces.first,
        );
      });
      
      // Load wards for current province
      if (_selectedProvince != null) {
        await _loadWards(_selectedProvince!.provinceCode);
      }
    } catch (e) {
      setState(() {
        _loadingProvinces = false;
      });
      print('Error loading provinces: $e');
    }
  }

  Future<void> _loadWards(String provinceCode) async {
    setState(() {
      _loadingWards = true;
      _selectedWard = null;
    });
    
    try {
      final wards = await LocationService.getWardsByProvinceCode(provinceCode);
      setState(() {
        _wards = wards;
        _loadingWards = false;
        
        // Find current ward if exists
        if (wards.isNotEmpty) {
          _selectedWard = wards.firstWhere(
            (w) => w.name == widget.user.ward,
            orElse: () => wards.first,
          );
        }
      });
    } catch (e) {
      setState(() {
        _loadingWards = false;
      });
      print('Error loading wards: $e');
    }
  }

  @override
  void dispose() {
    _fullNameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    if (_passwordController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng nhập mật khẩu để xác thực thay đổi'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Verify current password first
      final authResult = await AuthService.loginUser(widget.user.email, _passwordController.text);
      if (authResult == null || authResult['user'] == null) {
        throw Exception('Mật khẩu không chính xác');
      }

      // Check if new email already exists (if changed)
      final newEmail = _emailController.text.trim();
      bool emailChanged = newEmail != widget.user.email;
      
      if (emailChanged) {
        final existingUserByEmail = await MongoDBDirectService.getUserByEmail(newEmail);
        if (existingUserByEmail != null && existingUserByEmail.id != widget.user.id) {
          throw Exception('Email này đã được sử dụng bởi tài khoản khác');
        }
      }

      // Check if new phone already exists (if changed)
      final newPhone = _phoneController.text.trim();
      if (newPhone != widget.user.phone) {
        final existingUserByPhone = await MongoDBDirectService.getUserByPhone(newPhone);
        if (existingUserByPhone != null && existingUserByPhone.id != widget.user.id) {
          throw Exception('Số điện thoại này đã được sử dụng bởi tài khoản khác');
        }
      }

      // Update user information
      final updateData = {
        'fullName': _fullNameController.text.trim(),
        'gender': _selectedGender,
        'birthYear': _selectedBirthYear,
        'province': _selectedProvince?.name ?? widget.user.province,
        'provinceCode': _selectedProvince?.provinceCode ?? widget.user.provinceCode,
        'ward': _selectedWard?.name ?? widget.user.ward,
        'wardCode': _selectedWard?.wardCode ?? widget.user.wardCode,
        'phone': newPhone,
        'email': newEmail,
        'updatedAt': DateTime.now().toIso8601String(),
      };

      // If email changed, mark as unverified
      if (emailChanged) {
        updateData['isEmailVerified'] = false;
        updateData.remove('emailVerifiedAt');
      }

      final success = await MongoDBDirectService.updateUser(widget.user.id!, updateData);

      if (success) {
        // Update JWT with new user data
        final updatedUser = widget.user.copyWith(
          fullName: _fullNameController.text.trim(),
          gender: _selectedGender,
          birthYear: _selectedBirthYear,
          province: _selectedProvince?.name ?? widget.user.province,
          provinceCode: _selectedProvince?.provinceCode ?? widget.user.provinceCode,
          ward: _selectedWard?.name ?? widget.user.ward,
          wardCode: _selectedWard?.wardCode ?? widget.user.wardCode,
          phone: newPhone,
          email: newEmail,
        );

        // Generate new tokens with updated user info
        final accessToken = JWTService.generateAccessToken(updatedUser);
        final refreshToken = JWTService.generateRefreshToken(updatedUser);
        await JWTService.saveTokens(accessToken, refreshToken, updatedUser);

        if (mounted) {
          setState(() {
            _isEditing = false;
          });
          
          if (emailChanged) {
            // Show message about email verification
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Cập nhật thành công! Vui lòng xác thực email mới.'),
                backgroundColor: Colors.orange,
              ),
            );
            
            // Navigate to email verification screen
            Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => EmailVerificationScreen(
                  email: newEmail,
                  userName: updatedUser.fullName,
                  userId: updatedUser.id!,
                  onVerificationSuccess: () {
                    Navigator.of(context).pop(); // Close verification screen
                    Navigator.of(context).pop(updatedUser); // Return to previous screen
                  },
                ),
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Cập nhật thông tin thành công!'),
                backgroundColor: Colors.green,
              ),
            );
            
            // Return updated user to previous screen
            Navigator.of(context).pop(updatedUser);
          }
        }
      } else {
        throw Exception('Không thể cập nhật thông tin');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi cập nhật: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        _passwordController.clear();
      }
    }
  }

  void _toggleEdit() {
    setState(() {
      _isEditing = !_isEditing;
      if (!_isEditing) {
        // Reset form when canceling edit
        _initializeControllers();
        _passwordController.clear();
        
        // Reset location selections
        _selectedProvince = _provinces.firstWhere(
          (p) => p.name == widget.user.province,
          orElse: () => _provinces.isNotEmpty ? _provinces.first : Province(provinceCode: '', name: ''),
        );
        _selectedWard = _wards.firstWhere(
          (w) => w.name == widget.user.ward,
          orElse: () => _wards.isNotEmpty ? _wards.first : Ward(wardCode: '', wardName: '', provinceCode: ''),
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thông tin cá nhân'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: _toggleEdit,
              tooltip: 'Chỉnh sửa thông tin',
            ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Header
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    child: Column(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundColor: AppConstants.primaryBlue,
                          child: Text(
                            widget.user.fullName.isNotEmpty 
                              ? widget.user.fullName[0].toUpperCase()
                              : 'U',
                            style: const TextStyle(
                              fontSize: 36,
                              fontWeight: FontWeight.bold,
                              color: AppConstants.whiteText,
                            ),
                          ),
                        ),
                        const SizedBox(height: AppConstants.paddingMedium),
                        Text(
                          _isEditing ? 'Chỉnh sửa thông tin' : widget.user.fullName,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryBlue,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        if (!_isEditing) ...[
                          const SizedBox(height: AppConstants.paddingSmall),
                          Text(
                            widget.user.email,
                            style: const TextStyle(
                              fontSize: 16,
                              color: AppConstants.greyText,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.paddingLarge),

                // Form fields
                _buildFormField(
                  controller: _fullNameController,
                  label: 'Họ và tên',
                  icon: Icons.person,
                  enabled: _isEditing,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập họ và tên';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Gender dropdown
                DropdownButtonFormField<String>(
                  value: _selectedGender,
                  decoration: InputDecoration(
                    labelText: 'Giới tính',
                    prefixIcon: const Icon(Icons.wc),
                    border: const OutlineInputBorder(),
                    enabled: _isEditing,
                  ),
                  items: ['Nam', 'Nữ', 'Khác'].map((String value) {
                    return DropdownMenuItem<String>(
                      value: value,
                      child: Text(value),
                    );
                  }).toList(),
                  onChanged: _isEditing ? (String? newValue) {
                    setState(() {
                      _selectedGender = newValue!;
                    });
                  } : null,
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Birth year dropdown
                DropdownButtonFormField<int>(
                  value: _selectedBirthYear,
                  decoration: InputDecoration(
                    labelText: 'Năm sinh',
                    prefixIcon: const Icon(Icons.cake),
                    border: const OutlineInputBorder(),
                    enabled: _isEditing,
                  ),
                  items: List.generate(100, (index) {
                    final year = DateTime.now().year - index;
                    return DropdownMenuItem<int>(
                      value: year,
                      child: Text(year.toString()),
                    );
                  }),
                  onChanged: _isEditing ? (int? newValue) {
                    setState(() {
                      _selectedBirthYear = newValue!;
                    });
                  } : null,
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                _buildFormField(
                  controller: _phoneController,
                  label: 'Số điện thoại',
                  icon: Icons.phone,
                  enabled: _isEditing,
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập số điện thoại';
                    }
                    if (!RegExp(r'^[0-9]{10,11}$').hasMatch(value)) {
                      return 'Số điện thoại không hợp lệ';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                _buildFormField(
                  controller: _emailController,
                  label: 'Email',
                  icon: Icons.email,
                  enabled: _isEditing,
                  keyboardType: TextInputType.emailAddress,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập email';
                    }
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                      return 'Email không hợp lệ';
                    }
                    return null;
                  },
                ),

                // Email verification status
                _buildEmailVerificationStatus(),

                const SizedBox(height: AppConstants.paddingMedium),

                // Province dropdown
                _loadingProvinces
                    ? const Center(child: CircularProgressIndicator())
                    : DropdownButtonFormField<Province>(
                        value: _selectedProvince,
                        decoration: InputDecoration(
                          labelText: 'Tỉnh/Thành phố',
                          prefixIcon: const Icon(Icons.location_city),
                          border: const OutlineInputBorder(),
                          enabled: _isEditing,
                        ),
                        items: _provinces.map((Province province) {
                          return DropdownMenuItem<Province>(
                            value: province,
                            child: Text(province.name),
                          );
                        }).toList(),
                        onChanged: _isEditing ? (Province? newValue) async {
                          setState(() {
                            _selectedProvince = newValue;
                          });
                          if (newValue != null) {
                            await _loadWards(newValue.provinceCode);
                          }
                        } : null,
                        validator: (value) {
                          if (value == null) {
                            return 'Vui lòng chọn tỉnh/thành phố';
                          }
                          return null;
                        },
                      ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Ward dropdown
                _loadingWards
                    ? const Center(child: CircularProgressIndicator())
                    : DropdownButtonFormField<Ward>(
                        value: _selectedWard,
                        decoration: InputDecoration(
                          labelText: 'Phường/Xã',
                          prefixIcon: const Icon(Icons.location_on),
                          border: const OutlineInputBorder(),
                          enabled: _isEditing && _wards.isNotEmpty,
                        ),
                        items: _wards.map((Ward ward) {
                          return DropdownMenuItem<Ward>(
                            value: ward,
                            child: Text(ward.name),
                          );
                        }).toList(),
                        onChanged: _isEditing ? (Ward? newValue) {
                          setState(() {
                            _selectedWard = newValue;
                          });
                        } : null,
                        validator: (value) {
                          if (value == null) {
                            return 'Vui lòng chọn phường/xã';
                          }
                          return null;
                        },
                      ),

                if (_isEditing) ...[
                  const SizedBox(height: AppConstants.paddingLarge),
                  
                  // Password confirmation
                  TextFormField(
                    controller: _passwordController,
                    obscureText: _obscurePassword,
                    decoration: InputDecoration(
                      labelText: 'Mật khẩu xác thực',
                      hintText: 'Nhập mật khẩu hiện tại để xác thực',
                      prefixIcon: const Icon(Icons.lock),
                      suffixIcon: IconButton(
                        icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                        onPressed: () {
                          setState(() {
                            _obscurePassword = !_obscurePassword;
                          });
                        },
                      ),
                      border: const OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Vui lòng nhập mật khẩu để xác thực';
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: AppConstants.paddingLarge),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _isLoading ? null : _toggleEdit,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.grey),
                            padding: const EdgeInsets.symmetric(vertical: AppConstants.buttonVerticalPadding),
                          ),
                          child: const Text(
                            'Hủy',
                            style: TextStyle(
                              color: Colors.grey,
                              fontSize: AppConstants.buttonFontSize,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: AppConstants.paddingMedium),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _updateProfile,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppConstants.primaryBlue,
                            padding: const EdgeInsets.symmetric(vertical: AppConstants.buttonVerticalPadding),
                          ),
                          child: _isLoading
                              ? const CircularProgressIndicator(color: AppConstants.whiteText)
                              : const Text(
                                  'Cập nhật',
                                  style: TextStyle(
                                    color: AppConstants.whiteText,
                                    fontSize: AppConstants.buttonFontSize,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],

                const SizedBox(height: AppConstants.paddingLarge),

                // Account info
                if (!_isEditing)
                  Container(
                    padding: const EdgeInsets.all(AppConstants.paddingMedium),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '📋 Thông tin tài khoản:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: AppConstants.primaryBlue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text('• Tài khoản được tạo: ${_formatDate(widget.user.createdAt)}'),
<<<<<<< HEAD
                        Text('• ID: ${IdFormatter.formatId(widget.user.id)}'),
=======
                        Text('• ID: ${widget.user.id ?? 'N/A'}'),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                        Text('• Trạng thái: ${widget.user.isActive ? 'Hoạt động' : 'Tạm khóa'}'),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    required bool enabled,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      enabled: enabled,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon),
        border: const OutlineInputBorder(),
      ),
      validator: validator,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildEmailVerificationStatus() {
    if (!widget.user.isEmailVerified) {
      return Container(
        margin: const EdgeInsets.only(top: 8, bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Row(
          children: [
            Icon(Icons.warning_amber, color: Colors.orange.shade600, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Email chưa được xác thực',
                style: TextStyle(
                  color: Colors.orange.shade700,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => EmailVerificationScreen(
                      email: widget.user.email,
                      userName: widget.user.fullName,
                      userId: widget.user.id!,
                      onVerificationSuccess: () {
                        Navigator.of(context).pop(); // Close verification screen
                        setState(() {
                          // This will trigger a rebuild with updated verification status
                        });
                      },
                    ),
                  ),
                );
              },
              style: TextButton.styleFrom(
                foregroundColor: Colors.orange.shade700,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              ),
              child: const Text(
                'Xác thực ngay',
                style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        margin: const EdgeInsets.only(top: 8, bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.shade200),
        ),
        child: Row(
          children: [
            Icon(Icons.verified, color: Colors.green.shade600, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Email đã được xác thực',
                style: TextStyle(
                  color: Colors.green.shade700,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}
