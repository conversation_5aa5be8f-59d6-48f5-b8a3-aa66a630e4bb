import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/utils/back_navigation_helper.dart';
import '../../lib/constants/app_constants.dart';

void main() {
  group('BackNavigationHelper Tests', () {
    testWidgets('createAppBar should create consistent AppBar', (WidgetTester tester) async {
      // Test AppBar creation
      final appBar = BackNavigationHelper.createAppBar(
        title: 'Test Title',
        centerTitle: true,
      );

      expect(appBar.title, isA<Text>());
      expect((appBar.title as Text).data, 'Test Title');
      expect(appBar.backgroundColor, AppConstants.primaryBlue);
      expect(appBar.foregroundColor, AppConstants.whiteText);
      expect(appBar.centerTitle, true);
    });

    testWidgets('safeNavigateBack should check if can pop', (WidgetTester tester) async {
      // Create a test widget with navigation
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              appBar: AppBar(title: const Text('Home')),
              body: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        appBar: AppBar(title: const Text('Second')),
                        body: ElevatedButton(
                          onPressed: () => BackNavigationHelper.safeNavigateBack(context),
                          child: const Text('Back'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('Navigate'),
              ),
            ),
          ),
        ),
      );

      // Navigate to second screen
      await tester.tap(find.text('Navigate'));
      await tester.pumpAndSettle();

      // Verify we're on second screen
      expect(find.text('Second'), findsOneWidget);

      // Test safe navigate back
      await tester.tap(find.text('Back'));
      await tester.pumpAndSettle();

      // Verify we're back on home screen
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Second'), findsNothing);
    });

    testWidgets('showBackConfirmationDialog should show dialog', (WidgetTester tester) async {
      bool? result;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  result = await BackNavigationHelper.showBackConfirmationDialog(context);
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.text('Xác nhận'), findsOneWidget);
      expect(find.text('Thoát'), findsOneWidget);
      expect(find.text('Hủy'), findsOneWidget);

      // Tap cancel
      await tester.tap(find.text('Hủy'));
      await tester.pumpAndSettle();

      expect(result, false);
    });

    testWidgets('showBackConfirmationDialog should return true when confirmed', (WidgetTester tester) async {
      bool? result;

      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () async {
                  result = await BackNavigationHelper.showBackConfirmationDialog(context);
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Tap confirm
      await tester.tap(find.text('Thoát'));
      await tester.pumpAndSettle();

      expect(result, true);
    });

    test('ScreenType enum should have correct values', () {
      expect(ScreenType.normal, isA<ScreenType>());
      expect(ScreenType.form, isA<ScreenType>());
      expect(ScreenType.payment, isA<ScreenType>());
    });

    testWidgets('NavigationExtension should work correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              appBar: AppBar(title: const Text('Home')),
              body: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => Scaffold(
                        appBar: AppBar(title: const Text('Second')),
                        body: ElevatedButton(
                          onPressed: () => context.navigateBack(),
                          child: const Text('Back'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('Navigate'),
              ),
            ),
          ),
        ),
      );

      // Navigate to second screen
      await tester.tap(find.text('Navigate'));
      await tester.pumpAndSettle();

      // Verify we're on second screen
      expect(find.text('Second'), findsOneWidget);

      // Test extension method
      await tester.tap(find.text('Back'));
      await tester.pumpAndSettle();

      // Verify we're back on home screen
      expect(find.text('Home'), findsOneWidget);
      expect(find.text('Second'), findsNothing);
    });

    testWidgets('createFormWillPopScope should handle unsaved changes', (WidgetTester tester) async {
      bool hasUnsavedChanges = true;
      final key = GlobalKey();

      await tester.pumpWidget(
        MaterialApp(
          home: BackNavigationHelper.createFormWillPopScope(
            hasUnsavedChanges: hasUnsavedChanges,
            child: Scaffold(
              key: key,
              appBar: AppBar(title: const Text('Form')),
              body: const Text('Form Content'),
            ),
          ),
        ),
      );

      expect(find.text('Form'), findsOneWidget);
      expect(find.text('Form Content'), findsOneWidget);
    });

    testWidgets('createPaymentWillPopScope should handle payment in progress', (WidgetTester tester) async {
      bool isPaymentInProgress = true;
      final key = GlobalKey();

      await tester.pumpWidget(
        MaterialApp(
          home: BackNavigationHelper.createPaymentWillPopScope(
            isPaymentInProgress: isPaymentInProgress,
            child: Scaffold(
              key: key,
              appBar: AppBar(title: const Text('Payment')),
              body: const Text('Payment Content'),
            ),
          ),
        ),
      );

      expect(find.text('Payment'), findsOneWidget);
      expect(find.text('Payment Content'), findsOneWidget);
    });
  });
}
