class User {
  final String? id;
  final String fullName;
  final String gender;
  final int birthYear;
  final String province;
  final String provinceCode;
  final String ward;
  final String wardCode;
  final String phone;
  final String email;
  
  // Authentication fields
  final String? passwordHash;
  
  // Timestamps
  final DateTime createdAt;
  final DateTime? updatedAt;
  
  // JWT related
  final String? refreshToken;
  final DateTime? refreshTokenExpiry;
  
  // Status
  final bool isActive;
  final bool isEmailVerified;
  final bool isPhoneVerified;

  User({
    this.id,
    required this.fullName,
    required this.gender,
    required this.birthYear,
    required this.province,
    required this.provinceCode,
    required this.ward,
    required this.wardCode,
    required this.phone,
    required this.email,
    this.passwordHash,
    required this.createdAt,
    this.updatedAt,
    this.refreshToken,
    this.refreshTokenExpiry,
    this.isActive = true,
    this.isEmailVerified = false,
    this.isPhoneVerified = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    // Handle ID parsing more safely
    String? userId;
    if (json['_id'] != null) {
      if (json['_id'] is Map) {
        userId = json['_id']['\$oid'];
      } else {
        userId = json['_id'].toString();
      }
    }
    
    return User(
      id: userId,
      fullName: json['fullName'] ?? '',
      gender: json['gender'] ?? '',
      birthYear: json['birthYear'] ?? 0,
      province: json['province'] ?? '',
      provinceCode: json['provinceCode'] ?? '',
      ward: json['ward'] ?? '',
      wardCode: json['wardCode'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      passwordHash: json['passwordHash'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      refreshToken: json['refreshToken'],
      refreshTokenExpiry: json['refreshTokenExpiry'] != null 
          ? DateTime.parse(json['refreshTokenExpiry']) 
          : null,
      isActive: json['isActive'] ?? true,
      isEmailVerified: json['isEmailVerified'] ?? false,
      isPhoneVerified: json['isPhoneVerified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'fullName': fullName,
      'gender': gender,
      'birthYear': birthYear,
      'province': province,
      'provinceCode': provinceCode,
      'ward': ward,
      'wardCode': wardCode,
      'phone': phone,
      'email': email,
      if (passwordHash != null) 'passwordHash': passwordHash,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      if (refreshToken != null) 'refreshToken': refreshToken,
      if (refreshTokenExpiry != null) 'refreshTokenExpiry': refreshTokenExpiry!.toIso8601String(),
      'isActive': isActive,
      'isEmailVerified': isEmailVerified,
      'isPhoneVerified': isPhoneVerified,
    };
  }

  User copyWith({
    String? id,
    String? fullName,
    String? gender,
    int? birthYear,
    String? province,
    String? provinceCode,
    String? ward,
    String? wardCode,
    String? phone,
    String? email,
    String? passwordHash,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? refreshToken,
    DateTime? refreshTokenExpiry,
    bool? isActive,
    bool? isEmailVerified,
    bool? isPhoneVerified,
  }) {
    return User(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      gender: gender ?? this.gender,
      birthYear: birthYear ?? this.birthYear,
      province: province ?? this.province,
      provinceCode: provinceCode ?? this.provinceCode,
      ward: ward ?? this.ward,
      wardCode: wardCode ?? this.wardCode,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      passwordHash: passwordHash ?? this.passwordHash,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      refreshToken: refreshToken ?? this.refreshToken,
      refreshTokenExpiry: refreshTokenExpiry ?? this.refreshTokenExpiry,
      isActive: isActive ?? this.isActive,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
    );
  }
}
