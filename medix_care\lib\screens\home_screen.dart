import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/jwt_service.dart';
import '../models/user.dart';
import '../utils/back_navigation_helper.dart';
import 'registration_screen.dart';
import 'login_screen.dart';
import 'profile_screen.dart';
import 'appointment_screen.dart';
import 'my_appointments_screen.dart';
import 'debug_appointments_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh auth status when returning to this screen
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final user = await JWTService.getCurrentUser();
      setState(() {
        _currentUser = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentUser = null;
        _isLoading = false;
      });
    }
  }

  /// Helper method to close drawer if it's open
  void _closeDrawerIfOpen() {
    try {
      if (Scaffold.of(context).isDrawerOpen) {
        Navigator.of(context).pop();
      }
    } catch (e) {
      // If there's an issue accessing the scaffold, try to pop anyway
      // This handles cases where the method is called from different contexts
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
    }
  }

  Future<void> _logout() async {
    try {
      await JWTService.clearTokens();
      setState(() {
        _currentUser = null;
      });
      if (mounted) {
        _closeDrawerIfOpen();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đăng xuất thành công'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng xuất: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Unified navigation to login screen - works from both drawer and main screen
  void _navigateToLogin() {
    _closeDrawerIfOpen();
    BackNavigationHelper.navigateWithRefresh(
      context,
      const LoginScreen(),
      onReturn: _checkAuthStatus,
    );
  }

  /// Unified navigation to registration screen - works from both drawer and main screen
  void _navigateToRegister() {
    _closeDrawerIfOpen();
    BackNavigationHelper.navigateWithRefresh(
      context,
      const RegistrationScreen(),
      onReturn: _checkAuthStatus,
    );
  }

  /// Unified navigation to profile screen - works from both drawer and main screen
  void _navigateToProfile() {
    _closeDrawerIfOpen();
    if (_currentUser != null) {
      BackNavigationHelper.navigateWithRefresh(
        context,
        ProfileScreen(user: _currentUser!),
        onReturn: () {
          // Refresh auth status to get any profile updates
          _checkAuthStatus();
        },
      ).then((updatedUser) {
        if (updatedUser != null && updatedUser is User) {
          setState(() {
            _currentUser = updatedUser;
          });
        }
      });
    }
  }

  /// Unified navigation to my appointments screen - works from both drawer and main screen
  void _navigateToMyAppointments() {
    _closeDrawerIfOpen();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MyAppointmentsScreen(),
        settings: const RouteSettings(name: '/my_appointments'),
      ),
    );
  }

  /// Unified navigation to book appointment screen - works from both drawer and main screen
  void _navigateToBookAppointment() {
    _closeDrawerIfOpen();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AppointmentScreen(),
        settings: const RouteSettings(name: '/book_appointment'),
      ),
    );
  }

  /// Unified navigation to debug appointments screen - works from both drawer and main screen
  void _navigateToDebugAppointments() {
    _closeDrawerIfOpen();
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DebugAppointmentsScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
      ),
      drawer: _buildDrawer(),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : _buildBody(),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppConstants.primaryBlue,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${AppConstants.hospitalIcon} ${AppConstants.appName}',
                  style: const TextStyle(
                    color: AppConstants.whiteText,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _currentUser != null 
                    ? 'Xin chào, ${_currentUser!.fullName}'
                    : 'Chưa đăng nhập',
                  style: const TextStyle(
                    color: AppConstants.whiteText,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          if (_currentUser == null) ...[
            ListTile(
              leading: const Icon(Icons.login),
              title: const Text('Đăng nhập'),
              onTap: _navigateToLogin,
            ),
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('Đăng ký'),
              onTap: _navigateToRegister,
            ),
          ] else ...[
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Thông tin tài khoản'),
              subtitle: Text(_currentUser!.email),
              onTap: _navigateToProfile,
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Đặt lịch khám'),
              onTap: _navigateToBookAppointment,
            ),
            ListTile(
              leading: const Icon(Icons.list_alt),
              title: const Text('Lịch khám của tôi'),
              onTap: _navigateToMyAppointments,
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.bug_report, color: Colors.orange),
              title: const Text('Debug Appointments', style: TextStyle(color: Colors.orange)),
              onTap: _navigateToDebugAppointments,
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text('Đăng xuất', style: TextStyle(color: Colors.red)),
              onTap: _logout,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '${AppConstants.hospitalIcon} ${AppConstants.appName}',
            style: const TextStyle(
              fontSize: AppConstants.titleFontSize,
              fontWeight: FontWeight.bold,
              color: AppConstants.primaryBlue,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          const Text(
            AppConstants.appSubtitle,
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              color: AppConstants.greyText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Welcome message based on auth status
          if (_currentUser != null) ...[
            Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 60,
                      ),
                      const SizedBox(height: AppConstants.paddingMedium),
                      Text(
                        'Chào mừng trở lại!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        _currentUser!.fullName,
                        style: const TextStyle(
                          fontSize: 18,
                          color: AppConstants.greyText,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Action buttons for logged in users
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: _navigateToBookAppointment,
                      borderRadius: BorderRadius.circular(8),
                      child: const Padding(
                        padding: EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 40,
                              color: AppConstants.primaryBlue,
                            ),
                            SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              'Đặt lịch khám',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.primaryBlue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: _navigateToMyAppointments,
                      borderRadius: BorderRadius.circular(8),
                      child: const Padding(
                        padding: EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          children: [
                            Icon(
                              Icons.list_alt,
                              size: 40,
                              color: AppConstants.primaryBlue,
                            ),
                            SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              'Lịch khám của tôi',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.primaryBlue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConstants.paddingMedium),

            // Profile button for easy access
            SizedBox(
              width: double.infinity,
              child: Card(
                child: InkWell(
                  onTap: _navigateToProfile,
                  borderRadius: BorderRadius.circular(8),
                  child: const Padding(
                    padding: EdgeInsets.all(AppConstants.paddingMedium),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.person,
                          size: 24,
                          color: AppConstants.primaryBlue,
                        ),
                        SizedBox(width: AppConstants.paddingSmall),
                        Text(
                          'Thông tin tài khoản',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                            color: AppConstants.primaryBlue,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ] else ...[
            // Guest message - encouraging to use menu
            Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.menu,
                        color: AppConstants.primaryBlue,
                        size: 60,
                      ),
                      const SizedBox(height: AppConstants.paddingMedium),
                      const Text(
                        'Chào mừng bạn!',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      const Text(
                        'Sử dụng menu (☰) để đăng nhập hoặc đăng ký tài khoản',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppConstants.greyText,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
