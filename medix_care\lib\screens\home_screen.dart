import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/jwt_service.dart';
import '../models/user.dart';
import 'registration_screen.dart';
import 'login_screen.dart';
import 'profile_screen.dart';
import 'appointment_screen.dart';
import 'my_appointments_screen.dart';
import 'debug_appointments_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  User? _currentUser;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh auth status when returning to this screen
    _checkAuthStatus();
  }

  Future<void> _checkAuthStatus() async {
    try {
      final user = await JWTService.getCurrentUser();
      setState(() {
        _currentUser = user;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _currentUser = null;
        _isLoading = false;
      });
    }
  }

  Future<void> _logout() async {
    try {
      await JWTService.clearTokens();
      setState(() {
        _currentUser = null;
      });
      if (mounted) {
        Navigator.of(context).pop(); // Close drawer
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đăng xuất thành công'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng xuất: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _navigateToLogin() {
    Navigator.of(context).pop(); // Close drawer
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    ).then((_) => _checkAuthStatus()); // Refresh auth status when returning
  }

  void _navigateToRegister() {
    Navigator.of(context).pop(); // Close drawer
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const RegistrationScreen(),
      ),
    ).then((_) => _checkAuthStatus()); // Refresh auth status when returning
  }

  void _navigateToProfile() {
    Navigator.of(context).pop(); // Close drawer
    if (_currentUser != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ProfileScreen(user: _currentUser!),
        ),
      ).then((updatedUser) {
        if (updatedUser != null && updatedUser is User) {
          setState(() {
            _currentUser = updatedUser;
          });
        }
      });
    }
  }

  void _navigateToMyAppointments() {
    Navigator.of(context).pop(); // Close drawer
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const MyAppointmentsScreen(),
        settings: const RouteSettings(name: '/my_appointments'),
      ),
    );
  }

  void _navigateToBookAppointment() {
    Navigator.of(context).pop(); // Close drawer
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AppointmentScreen(),
        settings: const RouteSettings(name: '/book_appointment'),
      ),
    );
  }

  void _navigateToDebugAppointments() {
    Navigator.of(context).pop(); // Close drawer
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const DebugAppointmentsScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(AppConstants.appName),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
      ),
      drawer: _buildDrawer(),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : _buildBody(),
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: const BoxDecoration(
              color: AppConstants.primaryBlue,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${AppConstants.hospitalIcon} ${AppConstants.appName}',
                  style: const TextStyle(
                    color: AppConstants.whiteText,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _currentUser != null 
                    ? 'Xin chào, ${_currentUser!.fullName}'
                    : 'Chưa đăng nhập',
                  style: const TextStyle(
                    color: AppConstants.whiteText,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
          if (_currentUser == null) ...[
            ListTile(
              leading: const Icon(Icons.login),
              title: const Text('Đăng nhập'),
              onTap: _navigateToLogin,
            ),
            ListTile(
              leading: const Icon(Icons.person_add),
              title: const Text('Đăng ký'),
              onTap: _navigateToRegister,
            ),
          ] else ...[
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Thông tin tài khoản'),
              subtitle: Text(_currentUser!.email),
              onTap: () {
                Navigator.of(context).pop();
                _navigateToProfile();
              },
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today),
              title: const Text('Đặt lịch khám'),
              onTap: _navigateToBookAppointment,
            ),
            ListTile(
              leading: const Icon(Icons.list_alt),
              title: const Text('Lịch khám của tôi'),
              onTap: _navigateToMyAppointments,
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.bug_report, color: Colors.orange),
              title: const Text('Debug Appointments', style: TextStyle(color: Colors.orange)),
              onTap: _navigateToDebugAppointments,
            ),
            ListTile(
              leading: const Icon(Icons.logout, color: Colors.red),
              title: const Text('Đăng xuất', style: TextStyle(color: Colors.red)),
              onTap: _logout,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildBody() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.paddingMedium),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            '${AppConstants.hospitalIcon} ${AppConstants.appName}',
            style: const TextStyle(
              fontSize: AppConstants.titleFontSize,
              fontWeight: FontWeight.bold,
              color: AppConstants.primaryBlue,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingSmall),
          const Text(
            AppConstants.appSubtitle,
            style: TextStyle(
              fontSize: AppConstants.subtitleFontSize,
              color: AppConstants.greyText,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.paddingLarge),
          
          // Welcome message based on auth status
          if (_currentUser != null) ...[
            Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 60,
                      ),
                      const SizedBox(height: AppConstants.paddingMedium),
                      Text(
                        'Chào mừng trở lại!',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        _currentUser!.fullName,
                        style: const TextStyle(
                          fontSize: 18,
                          color: AppConstants.greyText,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
            
            // Action buttons for logged in users
            Row(
              children: [
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: _navigateToBookAppointment,
                      borderRadius: BorderRadius.circular(8),
                      child: const Padding(
                        padding: EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          children: [
                            Icon(
                              Icons.calendar_today,
                              size: 40,
                              color: AppConstants.primaryBlue,
                            ),
                            SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              'Đặt lịch khám',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.primaryBlue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Card(
                    child: InkWell(
                      onTap: _navigateToMyAppointments,
                      borderRadius: BorderRadius.circular(8),
                      child: const Padding(
                        padding: EdgeInsets.all(AppConstants.paddingMedium),
                        child: Column(
                          children: [
                            Icon(
                              Icons.list_alt,
                              size: 40,
                              color: AppConstants.primaryBlue,
                            ),
                            SizedBox(height: AppConstants.paddingSmall),
                            Text(
                              'Lịch khám của tôi',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: AppConstants.primaryBlue,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ] else ...[
            // Guest message - encouraging to use menu
            Center(
              child: Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.paddingMedium),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.menu,
                        color: AppConstants.primaryBlue,
                        size: 60,
                      ),
                      const SizedBox(height: AppConstants.paddingMedium),
                      const Text(
                        'Chào mừng bạn!',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppConstants.primaryBlue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      const Text(
                        'Sử dụng menu (☰) để đăng nhập hoặc đăng ký tài khoản',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppConstants.greyText,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
