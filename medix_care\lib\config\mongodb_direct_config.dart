class MongoDBConfig {
  // MongoDB Connection String Configuration
  // Sử dụng connection string trực tiếp với mongo_dart
  
  // MongoDB Connection String
  static const String connectionString = 'mongodb+srv://thanhhai3009hutech:<EMAIL>/medixcare?retryWrites=true&w=majority&appName=Cluster0';
  
  // Database name (từ connection string)
  static const String database = 'medixcare';
  
  // Collection names
  static const String usersCollection = 'users';
  static const String sessionsCollection = 'sessions';
  
  /// Parse connection info from connection string
  static Map<String, String> get connectionInfo {
    final uri = Uri.parse(connectionString);
    return {
      'host': uri.host,
      'database': database,
      'username': uri.userInfo.split(':')[0],
      'cluster': 'Cluster0',
    };
  }
  
  /// Validate configuration
  static bool isConfigured() {
    return connectionString.isNotEmpty && 
           connectionString.contains('mongodb+srv://');
  }
  
  /// Get configuration status message
  static String getConfigMessage() {
    if (!isConfigured()) {
      return '❌ MongoDB connection string chưa được cấu hình!';
    }
    
    final info = connectionInfo;
    return '''
✅ MongoDB được cấu hình với Connection String

Thông tin kết nối:
- Host: ${info['host']}
- Database: ${info['database']}
- Username: ${info['username']}
- Cluster: ${info['cluster']}

Sẵn sàng kết nối trực tiếp với MongoDB!
    ''';
  }
}
