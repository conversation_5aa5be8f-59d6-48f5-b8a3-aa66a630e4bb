class Department {
  final String? id;
  final String name;
  final String description;
  final List<String> doctorIds; // <PERSON>h sách ID của các bác sĩ trong khoa
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Department({
    this.id,
    required this.name,
    required this.description,
    required this.doctorIds,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory Department.fromJson(Map<String, dynamic> json) {
    // Handle ID parsing more safely
    String? departmentId;
    if (json['_id'] != null) {
      if (json['_id'] is Map) {
        departmentId = json['_id']['\$oid'];
      } else {
        departmentId = json['_id'].toString();
      }
    }

    return Department(
      id: departmentId,
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      doctorIds: List<String>.from(json['doctorIds'] ?? []),
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'name': name,
      'description': description,
      'doctorIds': doctorIds,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  Department copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? doctorIds,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Department(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      doctorIds: doctorIds ?? this.doctorIds,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() => name;
}
