# Google Sign In - SHA-1 Fingerprint Setup

## Lỗi ApiException: 10

Lỗi này x<PERSON>y ra khi:
1. SHA-1 fingerprint chưa đư<PERSON><PERSON> thêm vào Google Console
2. Package name không khớp
3. C<PERSON>u hình google-services.json không đúng

## C<PERSON>ch khắc phục:

### Bước 1: Lấy SHA-1 Fingerprint

#### Cho Debug Build:
```bash
# Windows
keytool -list -v -keystore "%USERPROFILE%\.android\debug.keystore" -alias androiddebugkey -storepass android -keypass android

# macOS/Linux
keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
```

#### Nếu debug.keystore chưa tồn tại:
```bash
# Tạo debug keystore
keytool -genkeypair -v -keystore ~/.android/debug.keystore -alias androiddebugkey -keyalg RSA -keysize 2048 -validity 10000 -storepass android -keypass android -dname "CN=Android Debug,O=Android,C=US"
```

#### Hoặc sử dụng Flutter:
```bash
cd android
./gradlew signingReport
```

### Bước 2: Thêm SHA-1 vào Google Console

1. Mở [Google Cloud Console](https://console.cloud.google.com/)
2. Chọn project: `pure-phalanx-454523-n9`
3. Vào **APIs & Services** > **Credentials**
4. Tìm **OAuth 2.0 Client IDs** cho Android
5. Thêm SHA-1 fingerprint vào **SHA certificate fingerprints**

### Bước 3: Tải google-services.json mới

1. Từ Google Console, tải file `google-services.json` mới
2. Copy vào `android/app/google-services.json`
3. Restart app

### Thông tin cấu hình:

- **Project ID**: pure-phalanx-454523-n9
- **Project Number**: 584071183060
- **Package Name**: com.example.medix_care
- **Web Client ID**: 584071183060-q4092k0063pi6brm19hfdvr6pjvpasin.apps.googleusercontent.com

### Test Mode:

Nếu vẫn gặp lỗi, có thể sử dụng Test Mode trong app để test flow đăng nhập mà không cần Google thực tế.
