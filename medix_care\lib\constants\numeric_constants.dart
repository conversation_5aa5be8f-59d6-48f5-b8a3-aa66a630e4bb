class NumericConstants {
  // Birth year range
  static const int minBirthYear = 1900;
  static const int maxBirthYear = 2024;
  
  // Phone number constants
  static const int phoneMinLength = 10;
  static const int phoneMaxLength = 11;
  
  // Text field max lengths
  static const int fullNameMaxLength = 50;
  static const int wardMaxLength = 30;
  static const int emailMaxLength = 100;
  
  // Form spacing (migrated to SpacingConstants)
  static const double formPadding = 16.0;      // Use SpacingConstants.paddingMedium
  static const double fieldSpacing = 16.0;     // Use SpacingConstants.spacingL
  static const double buttonHeight = 48.0;     // Use SizeConstants.buttonHeightMedium
  
  // Border radius (migrated to SizeConstants)
  static const double borderRadius = 8.0;      // Use SizeConstants.borderRadiusMedium
}
