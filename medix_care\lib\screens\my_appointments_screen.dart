import 'package:flutter/material.dart';
import '../models/appointment.dart';
import '../models/doctor.dart';
import '../models/department.dart';
import '../services/appointment_service.dart';
import '../services/jwt_service.dart';
import '../constants/appointment_constants.dart';
import '../constants/app_constants.dart';
<<<<<<< HEAD
import '../themes/app_bar_theme.dart' as custom_app_bar_theme;
import '../utils/back_navigation_helper.dart';
import '../utils/navigation_middleware.dart';
import '../utils/id_formatter.dart';
import 'payment_method_screen.dart';
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697

class MyAppointmentsScreen extends StatefulWidget {
  const MyAppointmentsScreen({super.key});

  @override
  State<MyAppointmentsScreen> createState() => _MyAppointmentsScreenState();
}

class _MyAppointmentsScreenState extends State<MyAppointmentsScreen> {
  List<AppointmentWithDetails> appointments = [];
  bool isLoading = true;
  String? error;
  String? currentUserId;

  @override
  void initState() {
    super.initState();
    _loadUserAppointments();
  }

  Future<void> _loadUserAppointments() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      // Get current user ID
      final user = await JWTService.getCurrentUser();
      if (user == null) {
        throw Exception('Người dùng chưa đăng nhập');
      }

      currentUserId = user.id;

      // Initialize service if not already done
      await AppointmentService.initialize();
      
      // Debug: Get ALL appointments first
      final allAppointments = await AppointmentService.getAllAppointments();
      print('🔍 DEBUG - Total appointments in database: ${allAppointments.length}');
      
      for (int i = 0; i < allAppointments.length && i < 5; i++) {
        final app = allAppointments[i];
        print('  All Appointment ${i + 1}:');
        print('    ID: ${app.id}');
        print('    Patient ID: "${app.patientId}"');
        print('    Date: ${app.appointmentDate}');
        print('    Time: ${app.appointmentTime}');
      }
      
      // Get appointments for current user
      final userAppointments = await AppointmentService.getAppointmentsByPatient(user.id!);
      
      // Debug log
      print('🔍 DEBUG - Loading user appointments:');
      print('  Current User ID: "${user.id}"');
      print('  User Full Name: ${user.fullName}');
      print('  User Email: ${user.email}');
      print('  Found ${userAppointments.length} appointments for this user');
      
      // Check if patientId matches exactly
      if (userAppointments.isEmpty && allAppointments.isNotEmpty) {
        print('🔍 DEBUG - Checking patientId matches:');
        for (final app in allAppointments) {
          final userIdStr = user.id!;
          final patientIdStr = app.patientId;
          final exactMatch = userIdStr == patientIdStr;
          final trimMatch = userIdStr.trim() == patientIdStr.trim();
          
          print('  Comparing:');
          print('    User ID: "$userIdStr" (length: ${userIdStr.length})');
          print('    Patient ID: "$patientIdStr" (length: ${patientIdStr.length})');
          print('    Exact match: $exactMatch');
          print('    Trim match: $trimMatch');
          
          if (exactMatch || trimMatch) {
            print('    ✅ MATCH FOUND!');
          }
        }
      }
      
      for (int i = 0; i < userAppointments.length; i++) {
        final app = userAppointments[i];
        print('  User Appointment ${i + 1}:');
        print('    ID: ${app.id}');
        print('    Patient ID: ${app.patientId}');
        print('    Date: ${app.appointmentDate}');
        print('    Time: ${app.appointmentTime}');
      }
      
      // Load doctor and department details for each appointment
      List<AppointmentWithDetails> appointmentsWithDetails = [];
      
      for (final appointment in userAppointments) {
        final doctor = await AppointmentService.getDoctorById(appointment.doctorId);
        final department = await AppointmentService.getDepartmentById(appointment.departmentId);
        
        appointmentsWithDetails.add(AppointmentWithDetails(
          appointment: appointment,
          doctor: doctor,
          department: department,
        ));
      }

      // Sort appointments by date (newest first)
      appointmentsWithDetails.sort((a, b) {
        final aDateTime = DateTime(
          a.appointment.appointmentDate.year,
          a.appointment.appointmentDate.month,
          a.appointment.appointmentDate.day,
          int.parse(a.appointment.appointmentTime.split(':')[0]),
          int.parse(a.appointment.appointmentTime.split(':')[1]),
        );
        final bDateTime = DateTime(
          b.appointment.appointmentDate.year,
          b.appointment.appointmentDate.month,
          b.appointment.appointmentDate.day,
          int.parse(b.appointment.appointmentTime.split(':')[0]),
          int.parse(b.appointment.appointmentTime.split(':')[1]),
        );
        return bDateTime.compareTo(aDateTime);
      });
      
      setState(() {
        appointments = appointmentsWithDetails;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  AppointmentDisplayStatus _getDisplayStatus(Appointment appointment) {
    final now = DateTime.now();
    final appointmentDateTime = DateTime(
      appointment.appointmentDate.year,
      appointment.appointmentDate.month,
      appointment.appointmentDate.day,
      int.parse(appointment.appointmentTime.split(':')[0]),
      int.parse(appointment.appointmentTime.split(':')[1]),
    );

    switch (appointment.status) {
      case AppointmentStatus.completed:
        return AppointmentDisplayStatus.completed;
      case AppointmentStatus.cancelled:
        return AppointmentDisplayStatus.cancelled;
      case AppointmentStatus.noShow:
        return AppointmentDisplayStatus.missed;
      default:
        // For pending or confirmed appointments
        if (appointmentDateTime.isBefore(now)) {
          return AppointmentDisplayStatus.missed;
        } else {
          return AppointmentDisplayStatus.pending;
        }
    }
  }

  Color _getStatusColor(AppointmentDisplayStatus status) {
    switch (status) {
      case AppointmentDisplayStatus.completed:
        return Colors.green;
      case AppointmentDisplayStatus.pending:
        return Colors.orange;
      case AppointmentDisplayStatus.missed:
        return Colors.red;
      case AppointmentDisplayStatus.cancelled:
        return Colors.grey;
    }
  }

  String _getStatusText(AppointmentDisplayStatus status) {
    switch (status) {
      case AppointmentDisplayStatus.completed:
        return 'Đã khám';
      case AppointmentDisplayStatus.pending:
        return 'Chờ khám';
      case AppointmentDisplayStatus.missed:
        return 'Quá hẹn';
      case AppointmentDisplayStatus.cancelled:
        return 'Đã hủy';
    }
  }

  IconData _getStatusIcon(AppointmentDisplayStatus status) {
    switch (status) {
      case AppointmentDisplayStatus.completed:
        return Icons.check_circle;
      case AppointmentDisplayStatus.pending:
        return Icons.schedule;
      case AppointmentDisplayStatus.missed:
        return Icons.error;
      case AppointmentDisplayStatus.cancelled:
        return Icons.cancel;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
<<<<<<< HEAD
      appBar: custom_app_bar_theme.AppBarTheme.create(
        title: 'Lịch khám của tôi',
=======
      appBar: AppBar(
        title: const Text('Lịch khám của tôi'),
        backgroundColor: AppConstants.primaryBlue,
        foregroundColor: AppConstants.whiteText,
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUserAppointments,
<<<<<<< HEAD
            tooltip: 'Làm mới',
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải lịch khám...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Lỗi tải dữ liệu',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadUserAppointments,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (appointments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.calendar_today,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'Chưa có lịch khám nào',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            const Text(
              'Các lịch khám đã đặt sẽ hiển thị ở đây',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
            
            // DEBUG INFO
            if (currentUserId != null) ...[
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(12),
                margin: const EdgeInsets.symmetric(horizontal: 32),
                decoration: BoxDecoration(
<<<<<<< HEAD
                  color: Colors.orange.withValues(alpha: 0.1),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
=======
                  color: Colors.orange.withOpacity(0.1),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    const Text(
                      'DEBUG INFO',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'User ID: "$currentUserId"',
                      style: const TextStyle(
                        fontFamily: 'Courier',
                        fontSize: 12,
                        color: Colors.orange,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text(
                      'Xem console logs để biết thêm chi tiết',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUserAppointments,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: appointments.length,
        itemBuilder: (context, index) {
          return _buildAppointmentCard(appointments[index]);
        },
      ),
    );
  }

<<<<<<< HEAD
  /// Cancel appointment with confirmation dialog
  Future<void> _cancelAppointment(AppointmentWithDetails appointmentDetails) async {
    final appointment = appointmentDetails.appointment;
    
    // Check if appointment can be cancelled
    if (!appointment.canCancel) {
      _showErrorDialog('Không thể hủy lịch hẹn này. Lịch hẹn phải được hủy ít nhất 2 giờ trước giờ khám.');
      return;
    }

    // Show confirmation dialog
    final confirmed = await _showCancelConfirmationDialog(appointmentDetails);
    if (!confirmed) return;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Cancel appointment
      final success = await AppointmentService.cancelAppointment(
        appointment.id!,
        reason: 'Bệnh nhân hủy lịch',
      );

      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();

      if (success) {
        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Đã hủy lịch hẹn thành công'),
              backgroundColor: Colors.green,
            ),
          );
        }
        
        // Reload appointments
        await _loadUserAppointments();
      } else {
        _showErrorDialog('Không thể hủy lịch hẹn. Vui lòng thử lại.');
      }
    } catch (e) {
      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();
      _showErrorDialog('Lỗi hủy lịch hẹn: ${e.toString()}');
    }
  }

  /// Show cancel confirmation dialog
  Future<bool> _showCancelConfirmationDialog(AppointmentWithDetails appointmentDetails) async {
    final appointment = appointmentDetails.appointment;
    final doctor = appointmentDetails.doctor;
    
    return await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận hủy lịch hẹn'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Bạn có chắc chắn muốn hủy lịch hẹn này không?'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bác sĩ: ${doctor?.title ?? ''} ${doctor?.fullName ?? 'N/A'}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Ngày: ${AppointmentConstants.formatDate(appointment.appointmentDate)}',
                  ),
                  Text('Giờ: ${appointment.appointmentTime}'),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Lưu ý: Việc hủy lịch hẹn có thể ảnh hưởng đến lịch trình khám bệnh của bạn.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.orange,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Không'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Hủy lịch hẹn'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// Show error dialog
  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thông báo'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  /// Navigate to payment screen
  void _navigateToPayment(AppointmentWithDetails appointmentDetails) {
    final appointment = appointmentDetails.appointment;
    final doctor = appointmentDetails.doctor;

    if (doctor == null) {
      _showErrorDialog('Không tìm thấy thông tin bác sĩ');
      return;
    }

    NavigationMiddleware.navigateWithAuthCheck(
      context,
      PaymentMethodScreen(
        appointment: appointment,
        doctor: doctor,
        doctorName: doctor.fullName,
      ),
      settings: const RouteSettings(name: '/payment_method'),
    ).then((_) {
      // Reload appointments when returning from payment
      if (mounted) {
        _loadUserAppointments();
      }
    });
  }

=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
  Widget _buildAppointmentCard(AppointmentWithDetails appointmentDetails) {
    final appointment = appointmentDetails.appointment;
    final doctor = appointmentDetails.doctor;
    final department = appointmentDetails.department;
    final status = _getDisplayStatus(appointment);
    final statusColor = _getStatusColor(status);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with status
            Row(
              children: [
                Icon(
                  _getStatusIcon(status),
                  color: statusColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _getStatusText(status),
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
<<<<<<< HEAD
                  'Mã: ${IdFormatter.formatId(appointment.id)}',
=======
                  'Mã: ${appointment.id?.substring(0, 8) ?? 'N/A'}',
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                  style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Date and time
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  AppointmentConstants.formatDate(appointment.appointmentDate),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                const SizedBox(width: 16),
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  appointment.appointmentTime,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Doctor information
            if (doctor != null) ...[
              Row(
                children: [
                  const Icon(Icons.person, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '${doctor.title} ${doctor.fullName}',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const SizedBox(width: 24),
                  Text(
                    doctor.specialization,
                    style: const TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
            const SizedBox(height: 8),

            // Department
            if (department != null) ...[
              Row(
                children: [
                  const Icon(Icons.local_hospital, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      department.name,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],

            // Symptoms
            if (appointment.symptoms.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.healing, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      appointment.symptoms,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            ],

            // Notes
            if (appointment.notes.isNotEmpty) ...[
              const SizedBox(height: 8),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(Icons.note, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      appointment.notes,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ],

            // Fee
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.payment, size: 16, color: Colors.grey),
                const SizedBox(width: 8),
                Text(
                  AppointmentConstants.formatCurrency(appointment.consultationFee),
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AppConstants.primaryBlue,
                  ),
                ),
                const Spacer(),
                if (!appointment.isPaid && status == AppointmentDisplayStatus.pending)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
<<<<<<< HEAD
                      color: Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
=======
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.orange.withOpacity(0.3)),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                    ),
                    child: const Text(
                      'Chưa thanh toán',
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
<<<<<<< HEAD

            // Action buttons
            if (status == AppointmentDisplayStatus.pending) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Payment button for unpaid appointments
                  if (!appointment.isPaid) ...[
                    ElevatedButton.icon(
                      onPressed: () => _navigateToPayment(appointmentDetails),
                      icon: const Icon(Icons.payment, size: 16),
                      label: const Text('Thanh toán'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  // Cancel button
                  if (appointment.canCancel)
                    TextButton.icon(
                      onPressed: () => _cancelAppointment(appointmentDetails),
                      icon: const Icon(Icons.cancel_outlined, size: 16),
                      label: const Text('Hủy lịch hẹn'),
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                    ),
                ],
              ),
            ],

            // Show cancelled reason if appointment is cancelled
            if (status == AppointmentDisplayStatus.cancelled && appointment.cancelledReason != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Lý do hủy:',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      appointment.cancelledReason!,
                      style: TextStyle(
                        color: Colors.red.shade700,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ],
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
          ],
        ),
      ),
    );
  }
}

// Helper classes
class AppointmentWithDetails {
  final Appointment appointment;
  final Doctor? doctor;
  final Department? department;

  AppointmentWithDetails({
    required this.appointment,
    this.doctor,
    this.department,
  });
}

enum AppointmentDisplayStatus {
  completed,  // Đã khám
  pending,    // Chờ khám
  missed,     // Quá hẹn
  cancelled,  // Đã hủy
}
