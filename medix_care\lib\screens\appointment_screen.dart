import 'package:flutter/material.dart';
import '../services/appointment_service.dart';
import '../models/department.dart';
import 'department_doctors_screen.dart';

class AppointmentScreen extends StatefulWidget {
  const AppointmentScreen({super.key});

  @override
  State<AppointmentScreen> createState() => _AppointmentScreenState();
}

class _AppointmentScreenState extends State<AppointmentScreen> {
  List<Department> departments = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadDepartments();
  }

  Future<void> _loadDepartments() async {
    try {
      setState(() {
        isLoading = true;
        error = null;
      });

      // Initialize service if not already done
      await AppointmentService.initialize();
      
      final loadedDepartments = await AppointmentService.getAllDepartments();
      
      setState(() {
        departments = loadedDepartments;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  Future<void> _showDoctors(Department department) async {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DepartmentDoctorsScreen(department: department),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Đặt lịch khám'),
        backgroundColor: const Color(0xFF2196F3),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDepartments,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Đang tải danh sách khoa...'),
          ],
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadDepartments,
              child: const Text('Thử lại'),
            ),
          ],
        ),
      );
    }

    if (departments.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_hospital_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text('Chưa có khoa nào'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadDepartments,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: departments.length,
        itemBuilder: (context, index) {
          final department = departments[index];
          return _buildDepartmentCard(department);
        },
      ),
    );
  }

  Widget _buildDepartmentCard(Department department) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
<<<<<<< HEAD
          backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
=======
          backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.2),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
          child: Icon(
            _getDepartmentIcon(department.name),
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
        title: Text(
          department.name,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Text(
          department.description,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: () => _showDoctors(department),
      ),
    );
  }

  IconData _getDepartmentIcon(String departmentName) {
    switch (departmentName.toLowerCase()) {
      case 'khoa nội':
        return Icons.favorite;
      case 'khoa ngoại':
        return Icons.healing;
      case 'khoa sản phụ khoa':
        return Icons.pregnant_woman;
      case 'khoa nhi':
        return Icons.child_care;
      case 'khoa mắt':
        return Icons.visibility;
      case 'khoa tai mũi họng':
        return Icons.hearing;
      case 'khoa da liễu':
        return Icons.face;
      case 'khoa răng hàm mặt':
        return Icons.mood;
      default:
        return Icons.medical_services;
    }
  }
}
