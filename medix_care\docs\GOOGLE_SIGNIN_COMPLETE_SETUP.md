# Google Sign In Setup - Step by Step

## Current Status: ⚠️ Simplified Configuration

The app currently uses simplified Google Sign In configuration without `google-services.json` to avoid build errors.

## To Enable Full Google Sign In:

### Step 1: Create OAuth 2.0 Client ID for Android

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Select project: `pure-phalanx-454523-n9`  
3. Navigate to **APIs & Services** → **Credentials**
4. Click **+ CREATE CREDENTIALS** → **OAuth 2.0 Client ID**
5. Select **Android** as Application type
6. Fill in:
   - **Name**: `MedixCare Android`
   - **Package name**: `com.example.medix_care`
   - **SHA-1 certificate fingerprint**: `8A:0B:18:0B:D8:2B:4D:E8:B6:F6:A6:22:E6:68:3B:A1:97:37:92:E3`

### Step 2: Download google-services.json

1. After creating the Android OAuth client, download the `google-services.json`
2. Place it in: `android/app/google-services.json`

### Step 3: Update Build Configuration

Add Google Services plugin back to `android/settings.gradle.kts`:
```kotlin
plugins {
    id("dev.flutter.flutter-plugin-loader") version "1.0.0"
    id("com.android.application") version "8.7.3" apply false
    id("org.jetbrains.kotlin.android") version "2.1.0" apply false
    id("com.google.gms.google-services") version "4.4.0" apply false
}
```

Add to `android/app/build.gradle.kts`:
```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
    id("com.google.gms.google-services")
}

dependencies {
    implementation(platform("com.google.firebase:firebase-bom:32.7.0"))
    implementation("com.google.android.gms:play-services-auth:20.7.0")
}
```

### Step 4: Update GoogleSignInService

Restore the serverClientId in `lib/services/google_sign_in_service.dart`:
```dart
static final GoogleSignIn _googleSignIn = GoogleSignIn(
  serverClientId: '584071183060-q4092k0063pi6brm19hfdvr6pjvpasin.apps.googleusercontent.com',
  scopes: ['email', 'profile'],
);
```

### Step 5: Clean and Rebuild

```bash
flutter clean
flutter pub get  
flutter run
```

## Current Workaround: Test Mode

- The Google Sign In button will show error due to missing configuration
- Click **"Test Mode"** in the error dialog to test the app with mock Google user
- This allows testing the complete login flow without real Google authentication

## Verification

When properly configured, Google Sign In should:
1. Open Google account picker
2. Allow user to select Google account  
3. Return to app with user logged in
4. Navigate to HomeScreen with success message

## Troubleshooting

If still getting ApiException: 10 after proper setup:
- Verify the Android OAuth 2.0 Client ID exists (not just Web)
- Ensure SHA-1 fingerprint matches exactly
- Check package name is `com.example.medix_care`
- Enable Google Sign-In API in the console
