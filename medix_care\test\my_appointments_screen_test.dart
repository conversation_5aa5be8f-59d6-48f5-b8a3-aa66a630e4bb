import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/screens/my_appointments_screen.dart';
import 'package:medix_care/models/appointment.dart';

void main() {
  group('My Appointments Screen Tests', () {
    test('should create display status enum correctly', () {
      // Test enum values
      expect(AppointmentDisplayStatus.completed, isNotNull);
      expect(AppointmentDisplayStatus.pending, isNotNull);
      expect(AppointmentDisplayStatus.missed, isNotNull);
      expect(AppointmentDisplayStatus.cancelled, isNotNull);
    });

    test('should create appointment with details wrapper', () {
<<<<<<< HEAD
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      // Create a test appointment
      final appointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
<<<<<<< HEAD
        appointmentDate: today,
=======
        appointmentDate: DateTime.now(),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
        appointmentTime: '09:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
<<<<<<< HEAD
        createdAt: now,
=======
        createdAt: DateTime.now(),
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      );

      final wrapper = AppointmentWithDetails(
        appointment: appointment,
        doctor: null,
        department: null,
      );

      expect(wrapper.appointment, equals(appointment));
      expect(wrapper.doctor, isNull);
      expect(wrapper.department, isNull);
    });
<<<<<<< HEAD

    test('should check if appointment can be cancelled', () {
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);
      
      // Create appointment tomorrow at 12:00 (can cancel)
      final futureAppointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: tomorrow,
        appointmentTime: '12:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.pending,
      );

      expect(futureAppointment.canCancel, isTrue);

      // Create appointment today in 1 hour (cannot cancel - less than 2 hours)
      final today = DateTime(now.year, now.month, now.day);
      final oneHourLater = now.add(const Duration(hours: 1));
      final soonAppointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: today,
        appointmentTime: '${oneHourLater.hour.toString().padLeft(2, '0')}:${oneHourLater.minute.toString().padLeft(2, '0')}',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.pending,
      );

      expect(soonAppointment.canCancel, isFalse);
    });

    test('should not allow cancelling completed appointments', () {
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);
      
      final appointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: tomorrow,
        appointmentTime: '15:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.completed,
      );

      expect(appointment.canCancel, isFalse);
    });

    test('should not allow cancelling already cancelled appointments', () {
      final now = DateTime.now();
      final tomorrow = DateTime(now.year, now.month, now.day + 1);
      
      final appointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: tomorrow,
        appointmentTime: '15:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Bệnh nhân hủy',
      );

      expect(appointment.canCancel, isFalse);
      expect(appointment.cancelledReason, equals('Bệnh nhân hủy'));
    });

    test('should create appointment with cancelled reason', () {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      final appointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: today,
        appointmentTime: '09:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Bệnh nhân có việc đột xuất',
      );

      expect(appointment.status, equals(AppointmentStatus.cancelled));
      expect(appointment.cancelledReason, equals('Bệnh nhân có việc đột xuất'));
      expect(appointment.statusVN, equals('Đã hủy'));
    });

    test('should copy appointment with new cancelled status', () {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);
      
      final originalAppointment = Appointment(
        patientId: 'test_patient',
        doctorId: 'test_doctor',
        departmentId: 'test_department',
        appointmentDate: today,
        appointmentTime: '09:00',
        symptoms: 'Test symptoms',
        notes: 'Test notes',
        consultationFee: 300000.0,
        createdAt: now,
        status: AppointmentStatus.pending,
      );

      final cancelledAppointment = originalAppointment.copyWith(
        status: AppointmentStatus.cancelled,
        cancelledReason: 'Bệnh nhân hủy lịch',
        updatedAt: now,
      );

      expect(cancelledAppointment.status, equals(AppointmentStatus.cancelled));
      expect(cancelledAppointment.cancelledReason, equals('Bệnh nhân hủy lịch'));
      expect(cancelledAppointment.updatedAt, isNotNull);
      
      // Original appointment should remain unchanged
      expect(originalAppointment.status, equals(AppointmentStatus.pending));
      expect(originalAppointment.cancelledReason, isNull);
    });
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
  });
}
