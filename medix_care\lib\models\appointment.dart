enum AppointmentStatus {
  pending,    // Ch<PERSON> xác nhận
  confirmed,  // Đ<PERSON> xác nhận
  completed,  // Đ<PERSON> hoàn thành
  cancelled,  // Đã hủy
  noShow,     // Không đến khám
}

class Appointment {
  final String? id;
  final String patientId; // ID của bệnh nhân (User)
  final String doctorId;
  final String departmentId;
  final DateTime appointmentDate; // Ngày khám
  final String appointmentTime; // <PERSON><PERSON><PERSON> khám (HH:mm)
  final AppointmentStatus status;
  final String symptoms; // Triệu chứng
  final String notes; // Ghi chú của bệnh nhân
  final String? doctorNotes; // Ghi chú của bác sĩ
<<<<<<< HEAD
  final String? cancelledReason; // Lý do hủy lịch
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
  final double consultationFee;
  final bool isPaid; // Đã thanh toán chưa
  final String? paymentMethod; // Ph<PERSON><PERSON>ng thức thanh toán
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? confirmedAt; // Thời gian xác nhận
  final DateTime? completedAt; // Thời gian hoàn thành

  Appointment({
    this.id,
    required this.patientId,
    required this.doctorId,
    required this.departmentId,
    required this.appointmentDate,
    required this.appointmentTime,
    this.status = AppointmentStatus.pending,
    required this.symptoms,
    required this.notes,
    this.doctorNotes,
<<<<<<< HEAD
    this.cancelledReason,
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
    required this.consultationFee,
    this.isPaid = false,
    this.paymentMethod,
    required this.createdAt,
    this.updatedAt,
    this.confirmedAt,
    this.completedAt,
  });

  factory Appointment.fromJson(Map<String, dynamic> json) {
    // Handle ID parsing more safely
    String? appointmentId;
    if (json['_id'] != null) {
      if (json['_id'] is Map) {
        appointmentId = json['_id']['\$oid'];
      } else {
        appointmentId = json['_id'].toString();
      }
    }

    return Appointment(
      id: appointmentId,
      patientId: json['patientId'] ?? '',
      doctorId: json['doctorId'] ?? '',
      departmentId: json['departmentId'] ?? '',
      appointmentDate: json['appointmentDate'] != null 
          ? DateTime.parse(json['appointmentDate']) 
          : DateTime.now(),
      appointmentTime: json['appointmentTime'] ?? '',
      status: AppointmentStatus.values.firstWhere(
        (e) => e.toString().split('.').last == (json['status'] ?? 'pending'),
        orElse: () => AppointmentStatus.pending,
      ),
      symptoms: json['symptoms'] ?? '',
      notes: json['notes'] ?? '',
      doctorNotes: json['doctorNotes'],
<<<<<<< HEAD
      cancelledReason: json['cancelledReason'],
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      consultationFee: (json['consultationFee'] ?? 0).toDouble(),
      isPaid: json['isPaid'] ?? false,
      paymentMethod: json['paymentMethod'],
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt']) 
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt']) 
          : null,
      confirmedAt: json['confirmedAt'] != null 
          ? DateTime.parse(json['confirmedAt']) 
          : null,
      completedAt: json['completedAt'] != null 
          ? DateTime.parse(json['completedAt']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'patientId': patientId,
      'doctorId': doctorId,
      'departmentId': departmentId,
      'appointmentDate': appointmentDate.toIso8601String(),
      'appointmentTime': appointmentTime,
      'status': status.toString().split('.').last,
      'symptoms': symptoms,
      'notes': notes,
      if (doctorNotes != null) 'doctorNotes': doctorNotes,
<<<<<<< HEAD
      if (cancelledReason != null) 'cancelledReason': cancelledReason,
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      'consultationFee': consultationFee,
      'isPaid': isPaid,
      if (paymentMethod != null) 'paymentMethod': paymentMethod,
      'createdAt': createdAt.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      if (confirmedAt != null) 'confirmedAt': confirmedAt!.toIso8601String(),
      if (completedAt != null) 'completedAt': completedAt!.toIso8601String(),
    };
  }

  /// Format ngày giờ khám để hiển thị
  String get formattedDateTime {
    final date = appointmentDate;
    final dayName = _getDayName(date.weekday);
    final dateStr = '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    return '$dayName, $dateStr lúc $appointmentTime';
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Thứ 2';
      case 2: return 'Thứ 3';
      case 3: return 'Thứ 4';
      case 4: return 'Thứ 5';
      case 5: return 'Thứ 6';
      case 6: return 'Thứ 7';
      case 7: return 'Chủ nhật';
      default: return '';
    }
  }

  /// Lấy tên trạng thái bằng tiếng Việt
  String get statusVN {
    switch (status) {
      case AppointmentStatus.pending:
        return 'Chờ xác nhận';
      case AppointmentStatus.confirmed:
        return 'Đã xác nhận';
      case AppointmentStatus.completed:
        return 'Đã hoàn thành';
      case AppointmentStatus.cancelled:
        return 'Đã hủy';
      case AppointmentStatus.noShow:
        return 'Không đến khám';
    }
  }

  /// Kiểm tra có thể hủy lịch không
  bool get canCancel {
    if (status == AppointmentStatus.cancelled || 
        status == AppointmentStatus.completed ||
        status == AppointmentStatus.noShow) {
      return false;
    }
    
    // Không thể hủy nếu còn ít hơn 2 giờ
    final appointmentDateTime = DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
      int.parse(appointmentTime.split(':')[0]),
      int.parse(appointmentTime.split(':')[1]),
    );
    
    return appointmentDateTime.difference(DateTime.now()).inHours >= 2;
  }

  Appointment copyWith({
    String? id,
    String? patientId,
    String? doctorId,
    String? departmentId,
    DateTime? appointmentDate,
    String? appointmentTime,
    AppointmentStatus? status,
    String? symptoms,
    String? notes,
    String? doctorNotes,
<<<<<<< HEAD
    String? cancelledReason,
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
    double? consultationFee,
    bool? isPaid,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? confirmedAt,
    DateTime? completedAt,
  }) {
    return Appointment(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      doctorId: doctorId ?? this.doctorId,
      departmentId: departmentId ?? this.departmentId,
      appointmentDate: appointmentDate ?? this.appointmentDate,
      appointmentTime: appointmentTime ?? this.appointmentTime,
      status: status ?? this.status,
      symptoms: symptoms ?? this.symptoms,
      notes: notes ?? this.notes,
      doctorNotes: doctorNotes ?? this.doctorNotes,
<<<<<<< HEAD
      cancelledReason: cancelledReason ?? this.cancelledReason,
=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      consultationFee: consultationFee ?? this.consultationFee,
      isPaid: isPaid ?? this.isPaid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  String toString() => 'Lịch khám $formattedDateTime - $statusVN';
}
