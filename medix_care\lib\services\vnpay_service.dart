import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/appointment.dart';

class VNPayService {
  // VNPay configuration
  static String get _vnpVersion => dotenv.env['VNPAY_VERSION'] ?? '2.1.0';
  static String get _vnpTmnCode => dotenv.env['VNPAY_TMN_CODE'] ?? 'DEMO_TMN_CODE'; 
  static String get _vnpHashSecret => dotenv.env['VNPAY_HASH_SECRET'] ?? 'DEMO_HASH_SECRET';
  static String get _vnpPayUrl => dotenv.env['VNPAY_PAY_URL'] ?? 'https://sandbox.vnpayment.vn/paymentv2/vpcpay.html';
  static String get _vnpReturnUrl => dotenv.env['VNPAY_RETURN_URL'] ?? 'https://medixcare.com/payment-result';
  static String get _vnpApiUrl => dotenv.env['VNPAY_API_URL'] ?? 'https://sandbox.vnpayment.vn/merchant_webapi/api/transaction';

  /// Tạo URL thanh toán VNPay cho một lịch hẹn
  static Future<String> createPaymentUrl(
    Appointment appointment, 
    String ipAddress, 
    {String locale = 'vn'}
  ) async {
    // Tạo mã đơn hàng 
    String orderId = "${DateTime.now().millisecondsSinceEpoch}_${appointment.id}";
    
    // Định dạng ngày giờ theo yêu cầu của VNPay
    String createDate = DateFormat('yyyyMMddHHmmss').format(DateTime.now());
    
    // Số tiền thanh toán (nhân với 100 vì VNPay yêu cầu số nguyên)
    int amount = (appointment.consultationFee * 100).round();
    
    // Tạo các tham số thanh toán
    Map<String, String> queryParams = {
      'vnp_Version': _vnpVersion,
      'vnp_Command': 'pay',
      'vnp_TmnCode': _vnpTmnCode,
      'vnp_Amount': amount.toString(),
      'vnp_CreateDate': createDate,
      'vnp_CurrCode': 'VND',
      'vnp_IpAddr': ipAddress,
      'vnp_Locale': locale,
      'vnp_OrderInfo': 'Thanh toan kham benh - MedixCare - $orderId',
      'vnp_OrderType': 'billpayment',
      'vnp_ReturnUrl': _vnpReturnUrl,
      'vnp_TxnRef': orderId,
    };

    // Sắp xếp các tham số theo alphabet để tạo chữ ký
    List<String> sortedKeys = queryParams.keys.toList()..sort();
    
    // Tạo chuỗi query đã được sắp xếp
    String hashData = sortedKeys.map((key) => '$key=${queryParams[key]}').join('&');
    
    // Tạo chữ ký
    String secureHash = hmacSha512(_vnpHashSecret, hashData);
    
    // Thêm chữ ký vào query params
    queryParams['vnp_SecureHash'] = secureHash;
    
    // Tạo URL thanh toán hoàn chỉnh
    String queryString = queryParams.entries.map((e) => '${e.key}=${e.value}').join('&');
    String paymentUrl = '$_vnpPayUrl?$queryString';
    
    return paymentUrl;
  }

  /// Mở URL thanh toán VNPay
  static Future<bool> openPaymentUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      return await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      return false;
    }
  }

  /// Kiểm tra kết quả thanh toán
  static bool verifyPaymentResult(Map<String, String> vnpParams) {
    // Lấy chữ ký từ tham số trả về
    String vnpSecureHash = vnpParams['vnp_SecureHash'] ?? '';
    
    // Xóa chữ ký để tạo chuỗi mới
    var paramMap = Map<String, String>.from(vnpParams);
    paramMap.remove('vnp_SecureHash');
    paramMap.remove('vnp_SecureHashType');
    
    // Sắp xếp các tham số theo alphabet
    List<String> sortedKeys = paramMap.keys.toList()..sort();
    
    // Tạo chuỗi để kiểm tra
    String hashData = sortedKeys.map((key) => '$key=${paramMap[key]}').join('&');
    
    // Tạo chữ ký mới để so sánh
    String secureHash = hmacSha512(_vnpHashSecret, hashData);
    
    // So sánh chữ ký
    bool isValidSignature = secureHash == vnpSecureHash;
    
    // Kiểm tra mã trạng thái
    String responseCode = vnpParams['vnp_ResponseCode'] ?? '';
    
    // Giao dịch thành công khi chữ ký hợp lệ và mã trạng thái là '00'
    return isValidSignature && responseCode == '00';
  }

  /// Tạo hàm băm HMAC SHA512
  static String hmacSha512(String key, String data) {
    var hmacKey = utf8.encode(key);
    var hmacData = utf8.encode(data);
    var hmacHash = Hmac(sha512, hmacKey).convert(hmacData);
    return hmacHash.toString();
  }

  /// Kiểm tra trạng thái thanh toán từ VNPay API
  static Future<bool> checkTransactionStatus(String orderId) async {
    try {
      // Tạo thời gian hiện tại theo định dạng yyyyMMddHHmmss
      String createDate = DateFormat('yyyyMMddHHmmss').format(DateTime.now());
      
      // Tạo tham số cho API kiểm tra trạng thái
      Map<String, String> queryParams = {
        'vnp_RequestId': _generateTransactionId(),
        'vnp_Version': _vnpVersion,
        'vnp_Command': 'querydr',
        'vnp_TmnCode': _vnpTmnCode,
        'vnp_TxnRef': orderId,
        'vnp_OrderInfo': 'Truy van GD ma $orderId',
        'vnp_TransactionDate': createDate,
        'vnp_CreateDate': createDate,
        'vnp_IpAddr': '127.0.0.1',
      };

      // Sắp xếp các tham số
      List<String> sortedKeys = queryParams.keys.toList()..sort();
      String hashData = sortedKeys.map((key) => '$key=${queryParams[key]}').join('&');
      
      // Tạo chữ ký
      String secureHash = hmacSha512(_vnpHashSecret, hashData);
      queryParams['vnp_SecureHash'] = secureHash;

      // Gửi request API
      final response = await http.post(
        Uri.parse(_vnpApiUrl),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(queryParams),
      );

      if (response.statusCode == 200) {
        final result = json.decode(response.body);
        // Giao dịch thành công khi mã trạng thái là '00'
        return result['vnp_ResponseCode'] == '00';
      }
      
      return false;
    } catch (e) {
      print('Error checking transaction status: $e');
      return false;
    }
  }

  /// Sinh mã giao dịch ngẫu nhiên
  static String _generateTransactionId() {
    Random random = Random();
    String result = '';
    for (var i = 0; i < 8; i++) {
      result += random.nextInt(10).toString();
    }
    return result;
  }

  /// Lấy mô tả trạng thái giao dịch VNPay
  static String getResponseDescription(String responseCode) {
    switch (responseCode) {
      case '00':
        return 'Giao dịch thành công';
      case '01':
        return 'Giao dịch đã tồn tại';
      case '02':
        return 'Merchant không hợp lệ';
      case '03':
        return 'Dữ liệu gửi sang không đúng định dạng';
      case '04':
        return 'Khởi tạo GD không thành công do Website đang bị tạm khóa';
      case '05':
        return 'Giao dịch không thành công do: Quý khách nhập sai mật khẩu quá số lần quy định';
      case '06':
        return 'Giao dịch không thành công do Quý khách nhập sai mật khẩu';
      case '07':
        return 'Giao dịch bị nghi ngờ gian lận';
      case '08':
        return 'Giao dịch không thành công do: Hệ thống Ngân hàng đang bảo trì';
      case '09':
        return 'Giao dịch không thành công do: Thẻ/Tài khoản của khách hàng chưa đăng ký dịch vụ';
      case '10':
        return 'Giao dịch không thành công do: Khách hàng xác thực thông tin thẻ/tài khoản không đúng quá 3 lần';
      case '11':
        return 'Giao dịch không thành công do: Đã hết hạn chờ thanh toán';
      case '24':
        return 'Giao dịch không thành công do: Khách hàng hủy giao dịch';
      default:
        return 'Lỗi không xác định';
    }
  }
}
