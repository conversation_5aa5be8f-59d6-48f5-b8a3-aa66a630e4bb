import 'package:flutter/material.dart';
import '../constants/app_constants.dart';
import '../services/auth_service.dart';
import '../themes/app_bar_theme.dart' as custom_app_bar_theme;
import '../utils/back_navigation_helper.dart';
import '../services/firebase_auth_service.dart';
import 'registration_screen.dart';
import 'home_screen.dart';
import 'email_verification_screen.dart';
import 'forgot_password_screen.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _firebaseAuthService = FirebaseAuthService();
  bool _isLoading = false;
  bool _obscurePassword = true;

  @override
  void dispose() {
    _emailPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  bool _isEmail(String input) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(input);
  }

  bool _isPhoneNumber(String input) {
    return RegExp(r'^[0-9]{10,11}$').hasMatch(input);
  }

  Future<void> _login() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final emailOrPhone = _emailPhoneController.text.trim();
      final password = _passwordController.text;

      // For now, we'll assume login by email only
      // You can extend this to support phone number login
      final result = await AuthService.loginUser(emailOrPhone, password);

      if (result != null) {
        if (result['error'] == 'EMAIL_NOT_VERIFIED') {
          // Navigate to email verification screen
          if (mounted) {
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => EmailVerificationScreen(
                  email: result['email'],
                  userName: result['userName'],
                  userId: result['userId'],
                ),
              ),
            );
          }
        } else if (result['user'] != null) {
          // Login successful
          if (mounted) {
            Navigator.of(context).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
              (route) => false,
            );
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Đăng nhập thành công!'),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else {
          throw Exception('Thông tin đăng nhập không chính xác');
        }
      } else {
        throw Exception('Thông tin đăng nhập không chính xác');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng nhập: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signInWithGoogle() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('Starting Google Sign In...');
      final result = await _firebaseAuthService.signInWithGoogle();
      
      print('Google Sign In result: $result');
      
      if (result['success']) {
        print('Google Sign In successful, navigating to HomeScreen');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Đăng nhập thành công! Chào mừng ${result['user']?['fullName'] ?? result['user']?['name'] ?? 'bạn'}'),
              backgroundColor: Colors.green,
            ),
          );
          
          // Small delay to ensure snackbar is shown
          await Future.delayed(const Duration(milliseconds: 500));
          
          // Navigate to HomeScreen and replace current screen
          if (mounted) {
            Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => const HomeScreen()),
            );
          }
        }
      } else {
        print('Google Sign In failed: ${result['message']}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result['message'] ?? 'Đăng nhập Google thất bại'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      print('Exception in _signInWithGoogle: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi đăng nhập Google: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToRegister() {
    BackNavigationHelper.navigateReplacement(
      context,
      const RegistrationScreen(),
    );
  }

  void _navigateToForgotPassword() {
    BackNavigationHelper.navigateWithRefresh(
      context,
      const ForgotPasswordScreen(),
    );
  }

  void _navigateToForgotPassword() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const ForgotPasswordScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: custom_app_bar_theme.AppBarTheme.createAuthAppBar(
        title: 'Đăng nhập',
        onBackPressed: () => BackNavigationHelper.safeNavigateBack(context),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.paddingMedium),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Title
                Text(
                  '${AppConstants.hospitalIcon} Đăng nhập ${AppConstants.appName}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppConstants.primaryBlue,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Email/Phone input
                TextFormField(
                  controller: _emailPhoneController,
                  keyboardType: TextInputType.emailAddress,
                  decoration: const InputDecoration(
                    labelText: 'Email hoặc số điện thoại',
                    hintText: 'Nhập email hoặc số điện thoại',
                    prefixIcon: Icon(Icons.person),
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập email hoặc số điện thoại';
                    }
                    if (!_isEmail(value) && !_isPhoneNumber(value)) {
                      return 'Email hoặc số điện thoại không hợp lệ';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Password input
                TextFormField(
                  controller: _passwordController,
                  obscureText: _obscurePassword,
                  decoration: InputDecoration(
                    labelText: 'Mật khẩu',
                    hintText: 'Nhập mật khẩu',
                    prefixIcon: const Icon(Icons.lock),
                    suffixIcon: IconButton(
                      icon: Icon(_obscurePassword ? Icons.visibility : Icons.visibility_off),
                      onPressed: () {
                        setState(() {
                          _obscurePassword = !_obscurePassword;
                        });
                      },
                    ),
                    border: const OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Vui lòng nhập mật khẩu';
                    }
                    if (value.length < 6) {
                      return 'Mật khẩu phải có ít nhất 6 ký tự';
                    }
                    return null;
                  },
                ),
                
                const SizedBox(height: AppConstants.paddingLarge),
                
                // Login button
                SizedBox(
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _login,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppConstants.primaryBlue,
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: AppConstants.whiteText)
                        : const Text(
                            'Đăng nhập',
                            style: TextStyle(
                              color: AppConstants.whiteText,
                              fontSize: AppConstants.buttonFontSize,
                            ),
                          ),
                  ),
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),

                // Divider with "OR"
                Row(
                  children: [
                    const Expanded(child: Divider()),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'HOẶC',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const Expanded(child: Divider()),
                  ],
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Google Sign In button
                SizedBox(
                  height: 50,
                  child: OutlinedButton.icon(
                    onPressed: _isLoading ? null : _signInWithGoogle,
                    icon: const Icon(
                      Icons.login,
                      color: Colors.red,
                      size: 24,
                    ),
                    label: const Text(
                      'Đăng nhập bằng Google',
                      style: TextStyle(
                        color: Colors.black87,
                        fontSize: AppConstants.buttonFontSize,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: Colors.grey),
                      backgroundColor: Colors.white,
                    ),
                  ),
                ),

                const SizedBox(height: AppConstants.paddingMedium),

                // Forgot password link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    TextButton(
                      onPressed: _navigateToForgotPassword,
                      child: const Text(
                        'Quên mật khẩu?',
                        style: TextStyle(
                          color: AppConstants.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: AppConstants.paddingMedium),
                
                // Register link
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text('Chưa có tài khoản? '),
                    TextButton(
                      onPressed: _navigateToRegister,
                      child: const Text(
                        'Đăng ký ngay',
                        style: TextStyle(
                          color: AppConstants.primaryBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const Spacer(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
