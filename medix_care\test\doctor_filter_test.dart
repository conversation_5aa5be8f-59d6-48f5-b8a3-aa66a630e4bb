import 'package:flutter_test/flutter_test.dart';
import 'package:medix_care/services/appointment_service.dart';
<<<<<<< HEAD
=======
import 'package:medix_care/models/department.dart';
import 'package:medix_care/models/doctor.dart';
import 'package:medix_care/models/doctor_schedule.dart';
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697

void main() {
  group('Doctor Filter Tests', () {
    setUpAll(() async {
      // Initialize the service before running tests
      final connected = await AppointmentService.initialize();
      expect(connected, isTrue, reason: 'Should connect to MongoDB successfully');
    });

    tearDownAll(() async {
      // Close connection after tests
      await AppointmentService.close();
    });

    test('should filter doctors by department and availability', () async {
      try {
        // Get all departments
        final departments = await AppointmentService.getAllDepartments();
        expect(departments, isNotEmpty);
        
<<<<<<< HEAD
        for (final department in departments) {
=======
        if (departments.isNotEmpty) {
          final department = departments.first;
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
          print('🏥 Testing department: ${department.name}');
          
          // Get doctors in this department
          final doctors = await AppointmentService.getDoctorsByDepartment(department.id!);
          print('👨‍⚕️ Found ${doctors.length} doctors in ${department.name}');
          
          for (final doctor in doctors) {
<<<<<<< HEAD
            print('  👨‍⚕️ Doctor: ${doctor.fullName}');
=======
            print('  - ${doctor.fullName}');
            print('    Available days: ${doctor.availableDays.join(', ')}');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
            
            // Get doctor schedules
            final schedules = await AppointmentService.getDoctorSchedules(doctor.id!);
            print('    Schedules: ${schedules.length}');
            
            for (final schedule in schedules) {
<<<<<<< HEAD
              print('      📅 ${schedule.dayOfWeek}: ${schedule.startTime} - ${schedule.endTime}');
            }
          }
        }
      } catch (e) {
        print('❌ Test failed: $e');
        rethrow;
      }
    });

    test('should find available doctors for specific criteria', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        
        for (final department in departments) {
          final doctors = await AppointmentService.getDoctorsByDepartment(department.id!);
=======
              print('      ${schedule.dayOfWeek}: ${schedule.startTime}-${schedule.endTime}');
              
              // Test time slot generation
              final timeSlots = schedule.generateTimeSlots();
              print('      Available slots: ${timeSlots.length} (${timeSlots.take(5).join(', ')}...)');
              
              expect(timeSlots, isNotEmpty, reason: 'Should have time slots');
            }
          }
          
          // Test filtering by day
          print('\n🔍 Testing filter by Monday:');
          final mondayDoctors = doctors.where((d) => d.availableDays.contains('Monday')).toList();
          print('   Found ${mondayDoctors.length} doctors available on Monday');
          
          for (final doctor in mondayDoctors) {
            print('   - ${doctor.fullName}');
          }
          
          // Test filtering by time slot
          print('\n🔍 Testing filter by 09:00 time slot:');
          List<Doctor> availableAt9AM = [];
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
          
          for (final doctor in doctors) {
            final schedules = await AppointmentService.getDoctorSchedules(doctor.id!);
            bool hasSlot = false;
            
            for (final schedule in schedules) {
<<<<<<< HEAD
              if (schedule.dayOfWeek == 'Wednesday' && 
                  schedule.startTime == '14:00') {
=======
              final timeSlots = schedule.generateTimeSlots();
              if (timeSlots.contains('09:00')) {
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
                hasSlot = true;
                break;
              }
            }
            
            if (hasSlot) {
<<<<<<< HEAD
              print('✅ Found available doctor: ${doctor.fullName} on Wednesday 14:00');
            }
          }
        }
      } catch (e) {
        print('❌ Test failed: $e');
        rethrow;
=======
              availableAt9AM.add(doctor);
            }
          }
          
          print('   Found ${availableAt9AM.length} doctors available at 09:00');
          for (final doctor in availableAt9AM) {
            print('   - ${doctor.fullName}');
          }
        }
        
      } catch (e) {
        print('❌ Filter test error: $e');
        fail('Filter test failed: $e');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      }
    });

    test('should validate filter logic for specific day and time', () async {
      try {
        final departments = await AppointmentService.getAllDepartments();
        
        if (departments.isNotEmpty) {
          final department = departments.first;
          final doctors = await AppointmentService.getDoctorsByDepartment(department.id!);
          
          // Test scenario: Find doctors available on Wednesday at 14:00
<<<<<<< HEAD
          for (final doctor in doctors) {
=======
          print('\n🎯 Scenario: Find doctors available on Wednesday at 14:00');
          
          List<Doctor> filteredDoctors = [];
          
          for (final doctor in doctors) {
            // Check if doctor works on Wednesday
            if (!doctor.availableDays.contains('Wednesday')) {
              continue;
            }
            
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
            // Check if doctor has 14:00 slot on Wednesday
            final schedules = await AppointmentService.getDoctorSchedules(doctor.id!);
            bool hasWednesday2PM = false;
            
            for (final schedule in schedules) {
              if (schedule.dayOfWeek == 'Wednesday') {
                final timeSlots = schedule.generateTimeSlots();
                if (timeSlots.contains('14:00')) {
                  hasWednesday2PM = true;
                  break;
                }
              }
            }
            
            if (hasWednesday2PM) {
<<<<<<< HEAD
              print('✅ ${doctor.fullName} available Wednesday 14:00');
            }
          }
        }
      } catch (e) {
        print('❌ Test failed: $e');
        rethrow;
=======
              filteredDoctors.add(doctor);
            }
          }
          
          print('   Result: ${filteredDoctors.length} doctors available');
          for (final doctor in filteredDoctors) {
            print('   ✅ ${doctor.fullName} - ${doctor.specialization}');
          }
          
          expect(filteredDoctors.length, greaterThanOrEqualTo(0));
        }
        
      } catch (e) {
        print('❌ Validation test error: $e');
        fail('Validation test failed: $e');
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
      }
    });
  });
}
<<<<<<< HEAD

=======
>>>>>>> 118e3a0b25d30c848a6ba7e2490564934d618697
