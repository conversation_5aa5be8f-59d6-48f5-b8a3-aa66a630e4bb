import '../services/appointment_service.dart';

class AppointmentServicePayment {
  /// Update appointment payment status
  static Future<bool> updateAppointmentPaymentStatus(
    String appointmentId, 
    {required bool isPaid, required String paymentMethod}
  ) async {
    try {
      // Use the main appointment service method directly
      return await AppointmentService.updateAppointmentPaymentStatus(
        appointmentId,
        isPaid: isPaid,
        paymentMethod: paymentMethod,
      );
    } catch (e) {
      print('❌ Error updating payment status: $e');
      return false;
    }
  }

  /// Check appointment payment status
  static Future<Map<String, dynamic>> getAppointmentPaymentInfo(String appointmentId) async {
    try {
      // Use the main appointment service method directly
      return await AppointmentService.getAppointmentPaymentInfo(appointmentId);
    } catch (e) {
      print('❌ Error getting payment info: $e');
      return {
        'error': e.toString(),
        'isPaid': false,
        'paymentMethod': null,
        'consultationFee': 0.0,
      };
    }
  }
}
