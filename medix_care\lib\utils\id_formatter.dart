class IdFormatter {
  /// Format ObjectId for display
  /// 
  /// This method takes an ObjectId string and formats it for display.
  /// Options:
  /// - short: true to show only first 8 characters (default)
  /// - hidden: true to hide completely and return empty string
  static String formatId(String? id, {bool short = true, bool hidden = false}) {
    if (id == null || id.isEmpty) {
      return 'N/A';
    }
    
    if (hidden) {
      return '';
    }

    // Extract hex part from ObjectId if needed
    String cleanedId = _cleanObjectId(id);
    
    // Return short version (first 8 chars) or full ID
    return short ? cleanedId.substring(0, min(8, cleanedId.length)) : cleanedId;
  }

  /// Clean ObjectId format from string 
  static String _cleanObjectId(String objectIdString) {
    if (objectIdString.isEmpty) {
      return objectIdString;
    }
    
    // Handle format: ObjectId("hexadecimal")
    if (objectIdString.startsWith('ObjectId("') && objectIdString.endsWith('")')) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    // Handle format: ObjectId('hexadecimal')
    if (objectIdString.startsWith("ObjectId('") && objectIdString.endsWith("')")) {
      return objectIdString.substring(10, objectIdString.length - 2);
    }
    
    return objectIdString;
  }
}

// Helper function for min to avoid importing dart:math
int min(int a, int b) => a < b ? a : b;
