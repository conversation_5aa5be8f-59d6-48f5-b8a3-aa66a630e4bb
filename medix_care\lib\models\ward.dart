class Ward {
  final String wardCode;
  final String wardName;
  final String provinceCode;
  final String? provinceName;

  Ward({
    required this.wardCode,
    required this.wardName,
    required this.provinceCode,
    this.provinceName,
  });

  factory Ward.fromJson(Map<String, dynamic> json) {
    return Ward(
      wardCode: json['ward_code']?.toString() ?? '',
      wardName: json['ward_name'] ?? json['name'] ?? '',
      provinceCode: json['province_code']?.toString() ?? '',
      provinceName: json['province_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ward_code': wardCode,
      'ward_name': wardName,
      'province_code': provinceCode,
      'province_name': provinceName,
    };
  }

  // For dropdown display
  String get name => wardName;

  @override
  String toString() => wardName;
}
