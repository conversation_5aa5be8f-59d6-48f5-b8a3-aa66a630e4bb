import '../constants/app_constants.dart';
import 'mongodb_direct_service.dart';

class AuthService {
  /// Register new user
  static Future<Map<String, dynamic>?> registerUser({
    required String fullName,
    required String gender,
    required int birthYear,
    required String province,
    required String provinceCode,
    required String ward,
    required String wardCode,
    required String phone,
    required String email,
    required String password,
    bool isPhoneVerified = false,
  }) async {
    try {
      print(AppConstants.usingDirectMongoDB);
      // Initialize connection first
      await MongoDBDirectService.initialize();
      
      final result = await MongoDBDirectService.registerUser(
        fullName: fullName,
        gender: gender,
        birthYear: birthYear,
        province: province,
        provinceCode: provinceCode,
        ward: ward,
        wardCode: wardCode,
        phone: phone,
        email: email,
        password: password,
        isPhoneVerified: isPhoneVerified,
      );
      
      return {
        'success': result != null,
        'user': result?['user'],
        'message': result != null ? AppConstants.registrationSuccess : AppConstants.registrationFailed,
        'error': result == null ? AppConstants.cannotCreateAccount : null,
      };
    } catch (e) {
      print('❌ AuthService registration error: $e');
      return {
        'success': false,
        'user': null,
        'message': AppConstants.registrationFailed,
        'error': e.toString(),
      };
    }
  }

  /// Login user with email or phone number
  static Future<Map<String, dynamic>?> loginUser(String emailOrPhone, String password) async {
    try {
      print('🔄 Using Direct MongoDB Service for login');
      // Initialize connection first
      await MongoDBDirectService.initialize();
      
      final result = await MongoDBDirectService.authenticateUser(emailOrPhone, password);
      
      // Check if email is verified
      if (result != null && result['user'] != null) {
        final user = result['user'];
        if (user.isEmailVerified == false) {
          return {
            'success': false,
            'user': null,
            'error': 'EMAIL_NOT_VERIFIED',
            'message': 'Vui lòng xác thực email trước khi đăng nhập',
            'email': user.email,
            'userName': user.fullName,
            'userId': user.id,
          };
        }
      }
      
      return result;
    } catch (e) {
      print('❌ AuthService login error: $e');
      rethrow;
    }
  }

  /// Test connection
  static Future<bool> testConnection() async {
    try {
      print('🔄 Testing Direct MongoDB Connection');
      return await MongoDBDirectService.testConnection();
    } catch (e) {
      print('❌ AuthService connection test error: $e');
      return false;
    }
  }

  /// Get service status
  static Map<String, dynamic> getServiceStatus() {
    return {
      'mode': 'DIRECT_MONGODB',
      'mongoConfigured': true,
      'configMessage': 'Using direct MongoDB connection',
      'connectionInfo': 'MongoDB Atlas via mongo_dart',
      'recommendation': '✅ Direct MongoDB connection mode',
    };
  }

  /// Get debug info
  static Future<Map<String, dynamic>> getDebugInfo() async {
    return {
      'mode': 'DIRECT_MONGODB',
      'status': 'Active',
      'connection': 'MongoDB Atlas',
      'package': 'mongo_dart',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}
