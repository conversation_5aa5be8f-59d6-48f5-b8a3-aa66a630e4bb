import 'package:flutter/material.dart';
import '../constants/appointment_constants.dart';

class DoctorFilterWidget extends StatefulWidget {
  final Function(DoctorFilter) onFilterChanged;
  final DoctorFilter initialFilter;
  final bool showTitle;

  const DoctorFilterWidget({
    super.key,
    required this.onFilterChanged,
    this.initialFilter = const DoctorFilter(),
    this.showTitle = true,
  });

  @override
  void initState() {
    super.initState();
    _currentFilter = widget.initialFilter;
  }

  void _updateFilter(DoctorFilter newFilter) {
    setState(() {
      _currentFilter = newFilter;
    });
    widget.onFilterChanged(newFilter);
  }

  void _clearFilters() {
    _updateFilter(const DoctorFilter());
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: widget.showTitle ? const EdgeInsets.all(16) : EdgeInsets.zero,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.showTitle) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Bộ lọc tìm kiếm',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (!_currentFilter.isEmpty)
                    TextButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear),
                      label: const Text('Xóa bộ lọc'),
                    ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            
            // Date Filter Section
            _buildDateFilterSection(),
            const SizedBox(height: 16),
            
            // Day of Week Filter Section
            _buildDayFilterSection(),
            const SizedBox(height: 16),
            
            // Time Slot Filter Section
            _buildTimeSlotSection(),
            
            if (!_currentFilter.isEmpty) ...[
              const SizedBox(height: 16),
              _buildFilterSummary(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDateFilterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chọn ngày khám:',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 16,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        _currentFilter.date != null
                            ? AppointmentConstants.formatDate(_currentFilter.date!)
                            : 'Chọn ngày',
                        style: TextStyle(
                          color: _currentFilter.date != null
                              ? Colors.black
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            if (_currentFilter.date != null)
              IconButton(
                onPressed: () => _updateFilter(_currentFilter.copyWith(
                  date: null,
                  clearDate: true,
                )),
                icon: const Icon(Icons.clear),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildDayFilterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Hoặc chọn thứ:',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AppointmentConstants.getAvailableDays().map((day) {
            final isSelected = _currentFilter.dayOfWeek == day;
            return FilterChip(
              label: Text(day),
              selected: isSelected,
              onSelected: (_currentFilter.date != null) ? null : (selected) {
                _updateFilter(_currentFilter.copyWith(
                  dayOfWeek: selected ? day : null,
                ));
              },

              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),

              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
        if (_currentFilter.date != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'Đã chọn ngày cụ thể, không thể chọn thêm thứ',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildTimeSlotSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Chọn giờ khám:',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: AppointmentConstants.getCommonTimeSlots().map((timeSlot) {
            final isSelected = _currentFilter.timeSlot == timeSlot;
            return FilterChip(
              label: Text(timeSlot),
              selected: isSelected,
              onSelected: (selected) {
                _updateFilter(_currentFilter.copyWith(
                  timeSlot: selected ? timeSlot : null,
                ));
              },
              selectedColor: Theme.of(context).primaryColor.withValues(alpha: 0.2),

              checkmarkColor: Theme.of(context).primaryColor,
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildFilterSummary() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),

        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.filter_list,
            color: Theme.of(context).primaryColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _currentFilter.description,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final now = DateTime.now();
    final firstDate = now.add(const Duration(days: 1)); // From tomorrow
    final lastDate = now.add(const Duration(days: 30)); // Up to 30 days

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _currentFilter.date ?? firstDate,
      firstDate: firstDate,
      lastDate: lastDate,
      helpText: 'Chọn ngày khám',
      cancelText: 'Hủy',
      confirmText: 'Chọn',
    );

    if (selectedDate != null) {
      _updateFilter(_currentFilter.copyWith(
        date: selectedDate,
        dayOfWeek: null, // Clear day of week when specific date is selected
      ));
    }
  }
}

class DoctorFilter {
  final DateTime? date;
  final String? dayOfWeek;
  final String? timeSlot;

  const DoctorFilter({
    this.date,
    this.dayOfWeek,
    this.timeSlot,
  });

  bool get isEmpty => date == null && dayOfWeek == null && timeSlot == null;
  
  bool get hasDateFilter => date != null || dayOfWeek != null;
  
  bool get hasTimeFilter => timeSlot != null;

  String get description {
    if (isEmpty) return 'Không có bộ lọc';
    
    List<String> parts = [];
    
    if (date != null) {
      parts.add('Ngày ${AppointmentConstants.formatDate(date!)}');
    } else if (dayOfWeek != null) {
      parts.add('$dayOfWeek');
    }
    
    if (timeSlot != null) {
      parts.add('Lúc $timeSlot');
    }
    
    return parts.join(' - ');
  }

  DoctorFilter copyWith({
    DateTime? date,
    String? dayOfWeek,
    String? timeSlot,
    bool clearDate = false,
  }) {
    return DoctorFilter(
      date: clearDate ? null : (date ?? this.date),
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      timeSlot: timeSlot ?? this.timeSlot,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DoctorFilter &&
        other.date == date &&
        other.dayOfWeek == dayOfWeek &&
        other.timeSlot == timeSlot;
  }

  @override
  int get hashCode => Object.hash(date, dayOfWeek, timeSlot);

  @override
  String toString() {
    return 'DoctorFilter(date: $date, dayOfWeek: $dayOfWeek, timeSlot: $timeSlot)';
  }
}
